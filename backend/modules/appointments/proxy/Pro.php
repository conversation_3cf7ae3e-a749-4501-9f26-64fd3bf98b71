<?php
namespace Bookly\Backend\Modules\Appointments\Proxy;

use Bookly\Lib;

/**
 * @method static void renderExportButton() Render export button.
 * @method static void renderExportDialog( array $datatables ) Render export dialog.
 * @method static void renderPrintButton() Render print button.
 * @method static void renderPrintDialog( array $datatables ) Render print dialog.
 */
abstract class Pro extends Lib\Base\Proxy
{

}