/*!
 * EventCalendar v4.4.1
 * https://github.com/vkurko/calendar
 */
var EventCalendar=function(rt){"use strict";var nl=rt=>{throw TypeError(rt)};var rl=(rt,pt,Ct)=>pt.has(rt)||nl("Cannot "+Ct);var st=(rt,pt,Ct)=>(rl(rt,pt,"read from private field"),Ct?Ct.call(rt):pt.get(rt)),Sr=(rt,pt,Ct)=>pt.has(rt)?nl("Cannot add the same private member more than once"):pt instanceof WeakSet?pt.add(rt):pt.set(rt,Ct);var il=(rt,pt,Ct)=>(rl(rt,pt,"access private method"),Ct);var Wo,Qn,Ut,bn,si,al;var Ct=Array.isArray,ol=Array.prototype.indexOf,mi=Array.from,ga=Object.defineProperty,wn=Object.getOwnPropertyDescriptor,ya=Object.getOwnPropertyDescriptors,ll=Object.prototype,sl=Array.prototype,gi=Object.getPrototypeOf,$a=Object.isExtensible;const bt=()=>{};function ul(e){return e()}function ir(e){for(var t=0;t<e.length;t++)e[t]()}function Mn(e,t){if(Array.isArray(e))return e;if(t===void 0||!(Symbol.iterator in e))return Array.from(e);const n=[];for(const r of e)if(n.push(r),n.length===t)break;return n}const Ot=2,ba=4,Rr=8,yi=16,Qt=32,Hn=64,Lr=128,wt=256,Ar=512,ut=1024,jt=2048,dn=4096,en=8192,Mr=16384,dl=32768,ar=65536,cl=1<<17,fl=1<<19,wa=1<<20,$i=1<<21,Dn=Symbol("$state"),vl=Symbol("legacy props"),hl=Symbol("");function Da(e){return e===this.v}function bi(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function wi(e){return!bi(e,this.v)}function _l(e){throw new Error("https://svelte.dev/e/effect_in_teardown")}function ml(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function gl(e){throw new Error("https://svelte.dev/e/effect_orphan")}function yl(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function $l(e){throw new Error("https://svelte.dev/e/props_invalid_value")}function bl(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function wl(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Dl(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let Pn=!1,Pc=!1;function El(){Pn=!0}const Di=1,Ei=2,Ea=4,kl=8,Tl=16,pl=1,Cl=2,xl=4,Sl=8,Rl=16,Ll=1,Al=2,_t=Symbol(),Ml="http://www.w3.org/1999/xhtml";function ka(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let Le=null;function Ta(e){Le=e}function be(e){return pa().get(e)}function Hl(e,t){return pa().set(e,t),t}function fe(e,t=!1,n){var r=Le={p:Le,c:null,d:!1,e:null,m:!1,s:e,x:null,l:null};Pn&&!t&&(Le.l={s:null,u:null,r1:[],r2:fn(!1)}),Fr(()=>{r.d=!0})}function ve(e){const t=Le;if(t!==null){e!==void 0&&(t.x=e);const l=t.e;if(l!==null){var n=Fe,r=Ne;t.e=null;try{for(var i=0;i<l.length;i++){var o=l[i];hn(o.effect),Yt(o.reaction),lr(o.fn)}}finally{hn(n),Yt(r)}}Le=t.p,t.m=!0}return e||{}}function Hr(){return!Pn||Le!==null&&Le.l===null}function pa(e){return Le===null&&ka(),Le.c??(Le.c=new Map(Pl(Le)||void 0))}function Pl(e){let t=e.p;for(;t!==null;){const n=t.c;if(n!==null)return n;t=t.p}return null}function tn(e){if(typeof e!="object"||e===null||Dn in e)return e;const t=gi(e);if(t!==ll&&t!==sl)return e;var n=new Map,r=Ct(e),i=He(0),o=Ne,l=d=>{var s=Ne;Yt(o);var f=d();return Yt(s),f};return r&&n.set("length",He(e.length)),new Proxy(e,{defineProperty(d,s,f){(!("value"in f)||f.configurable===!1||f.enumerable===!1||f.writable===!1)&&bl();var v=n.get(s);return v===void 0?(v=l(()=>He(f.value)),n.set(s,v)):j(v,l(()=>tn(f.value))),!0},deleteProperty(d,s){var f=n.get(s);if(f===void 0)s in d&&(n.set(s,l(()=>He(_t))),ki(i));else{if(r&&typeof s=="string"){var v=n.get("length"),h=Number(s);Number.isInteger(h)&&h<v.v&&j(v,h)}j(f,_t),ki(i)}return!0},get(d,s,f){var m;if(s===Dn)return e;var v=n.get(s),h=s in d;if(v===void 0&&(!h||(m=wn(d,s))!=null&&m.writable)&&(v=l(()=>He(tn(h?d[s]:_t))),n.set(s,v)),v!==void 0){var c=a(v);return c===_t?void 0:c}return Reflect.get(d,s,f)},getOwnPropertyDescriptor(d,s){var f=Reflect.getOwnPropertyDescriptor(d,s);if(f&&"value"in f){var v=n.get(s);v&&(f.value=a(v))}else if(f===void 0){var h=n.get(s),c=h==null?void 0:h.v;if(h!==void 0&&c!==_t)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return f},has(d,s){var c;if(s===Dn)return!0;var f=n.get(s),v=f!==void 0&&f.v!==_t||Reflect.has(d,s);if(f!==void 0||Fe!==null&&(!v||(c=wn(d,s))!=null&&c.writable)){f===void 0&&(f=l(()=>He(v?tn(d[s]):_t)),n.set(s,f));var h=a(f);if(h===_t)return!1}return v},set(d,s,f,v){var R;var h=n.get(s),c=s in d;if(r&&s==="length")for(var m=f;m<h.v;m+=1){var _=n.get(m+"");_!==void 0?j(_,_t):m in d&&(_=l(()=>He(_t)),n.set(m+"",_))}h===void 0?(!c||(R=wn(d,s))!=null&&R.writable)&&(h=l(()=>He(void 0)),j(h,l(()=>tn(f))),n.set(s,h)):(c=h.v!==_t,j(h,l(()=>tn(f))));var y=Reflect.getOwnPropertyDescriptor(d,s);if(y!=null&&y.set&&y.set.call(v,f),!c){if(r&&typeof s=="string"){var g=n.get("length"),$=Number(s);Number.isInteger($)&&$>=g.v&&j(g,$+1)}ki(i)}return!0},ownKeys(d){a(i);var s=Reflect.ownKeys(d).filter(h=>{var c=n.get(h);return c===void 0||c.v!==_t});for(var[f,v]of n)v.v!==_t&&!(f in d)&&s.push(f);return s},setPrototypeOf(){wl()}})}function ki(e,t=1){j(e,e.v+t)}function In(e){var t=Ot|jt,n=Ne!==null&&(Ne.f&Ot)!==0?Ne:null;return Fe===null||n!==null&&(n.f&wt)!==0?t|=wt:Fe.f|=wa,{ctx:Le,deps:null,effects:null,equals:Da,f:t,fn:e,reactions:null,rv:0,v:null,wv:0,parent:n??Fe}}function k(e){const t=In(e);return za(t),t}function cn(e){const t=In(e);return t.equals=wi,t}function Ca(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)nn(t[n])}}function Il(e){for(var t=e.parent;t!==null;){if((t.f&Ot)===0)return t;t=t.parent}return null}function xa(e){var t,n=Fe;hn(Il(e));try{Ca(e),t=Va(e)}finally{hn(n)}return t}function Sa(e){var t=xa(e);if(e.equals(t)||(e.v=t,e.wv=Ga()),!On){var n=(_n||(e.f&wt)!==0)&&e.deps!==null?dn:ut;St(e,n)}}const or=new Map;function fn(e,t){var n={f:0,v:e,reactions:null,equals:Da,rv:0,wv:0};return n}function He(e,t){const n=fn(e);return za(n),n}function vn(e,t=!1){var r;const n=fn(e);return t||(n.equals=wi),Pn&&Le!==null&&Le.l!==null&&((r=Le.l).s??(r.s=[])).push(n),n}function j(e,t,n=!1){Ne!==null&&!Gt&&Hr()&&(Ne.f&(Ot|yi))!==0&&!(dt!=null&&dt.includes(e))&&Dl();let r=n?tn(t):t;return Ti(e,r)}function Ti(e,t){if(!e.equals(t)){var n=e.v;On?or.set(e,t):or.set(e,n),e.v=t,(e.f&Ot)!==0&&((e.f&jt)!==0&&xa(e),St(e,(e.f&wt)===0?ut:dn)),e.wv=Ga(),Ra(e,jt),Hr()&&Fe!==null&&(Fe.f&ut)!==0&&(Fe.f&(Qt|Hn))===0&&(xt===null?Xl([e]):xt.push(e))}return t}function Ra(e,t){var n=e.reactions;if(n!==null)for(var r=Hr(),i=n.length,o=0;o<i;o++){var l=n[o],d=l.f;(d&jt)===0&&(!r&&l===Fe||(St(l,t),(d&(ut|wt))!==0&&((d&Ot)!==0?Ra(l,dn):Vr(l))))}}let Nl=!1;var mt,La,Aa,Ma;function Fl(){if(mt===void 0){mt=window,La=/Firefox/.test(navigator.userAgent);var e=Element.prototype,t=Node.prototype,n=Text.prototype;Aa=wn(t,"firstChild").get,Ma=wn(t,"nextSibling").get,$a(e)&&(e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__style=void 0,e.__e=void 0),$a(n)&&(n.__t=void 0)}}function Pr(e=""){return document.createTextNode(e)}function Ir(e){return Aa.call(e)}function Nr(e){return Ma.call(e)}function X(e,t){return Ir(e)}function se(e,t){{var n=Ir(e);return n instanceof Comment&&n.data===""?Nr(n):n}}function ie(e,t=1,n=!1){let r=e;for(;t--;)r=Nr(r);return r}function Ol(e){e.textContent=""}function Ha(e){Fe===null&&Ne===null&&gl(),Ne!==null&&(Ne.f&wt)!==0&&Fe===null&&ml(),On&&_l()}function Ul(e,t){var n=t.last;n===null?t.last=t.first=e:(n.next=e,e.prev=n,t.last=e)}function Nn(e,t,n,r=!0){var i=Fe,o={ctx:Le,deps:null,nodes_start:null,nodes_end:null,f:e|jt,first:null,fn:t,last:null,next:null,parent:i,prev:null,teardown:null,transitions:null,wv:0};if(n)try{qr(o),o.f|=dl}catch(s){throw nn(o),s}else t!==null&&Vr(o);var l=n&&o.deps===null&&o.first===null&&o.nodes_start===null&&o.teardown===null&&(o.f&(wa|Lr))===0;if(!l&&r&&(i!==null&&Ul(o,i),Ne!==null&&(Ne.f&Ot)!==0)){var d=Ne;(d.effects??(d.effects=[])).push(o)}return o}function Fr(e){const t=Nn(Rr,null,!1);return St(t,ut),t.teardown=e,t}function Ze(e){Ha();var t=Fe!==null&&(Fe.f&Qt)!==0&&Le!==null&&!Le.m;if(t){var n=Le;(n.e??(n.e=[])).push({fn:e,effect:Fe,reaction:Ne})}else{var r=lr(e);return r}}function Fn(e){return Ha(),sr(e)}function Wl(e){const t=Nn(Hn,e,!0);return(n={})=>new Promise(r=>{n.outro?dr(t,()=>{nn(t),r(void 0)}):(nn(t),r(void 0))})}function lr(e){return Nn(ba,e,!1)}function zl(e,t){var n=Le,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=sr(()=>{e(),!r.ran&&(r.ran=!0,j(n.l.r2,!0),Z(t))})}function Bl(){var e=Le;sr(()=>{if(a(e.l.r2)){for(var t of e.l.r1){var n=t.effect;(n.f&ut)!==0&&St(n,dn),Un(n)&&qr(n),t.ran=!1}e.l.r2.v=!1}})}function sr(e){return Nn(Rr,e,!0)}function q(e,t=[],n=In){const r=t.map(n);return ur(()=>e(...r.map(a)))}function ur(e,t=0){return Nn(Rr|yi|t,e,!0)}function En(e,t=!0){return Nn(Rr|Qt,e,!0,t)}function Pa(e){var t=e.teardown;if(t!==null){const n=On,r=Ne;Wa(!0),Yt(null);try{t.call(null)}finally{Wa(n),Yt(r)}}}function Ia(e,t=!1){var n=e.first;for(e.first=e.last=null;n!==null;){var r=n.next;(n.f&Hn)!==0?n.parent=null:nn(n,t),n=r}}function Gl(e){for(var t=e.first;t!==null;){var n=t.next;(t.f&Qt)===0&&nn(t),t=n}}function nn(e,t=!0){var n=!1;(t||(e.f&fl)!==0)&&e.nodes_start!==null&&(Yl(e.nodes_start,e.nodes_end),n=!0),Ia(e,t&&!n),Yr(e,0),St(e,Mr);var r=e.transitions;if(r!==null)for(const o of r)o.stop();Pa(e);var i=e.parent;i!==null&&i.first!==null&&Na(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=null}function Yl(e,t){for(;e!==null;){var n=e===t?null:Nr(e);e.remove(),e=n}}function Na(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function dr(e,t){var n=[];pi(e,n,!0),Fa(n,()=>{nn(e),t&&t()})}function Fa(e,t){var n=e.length;if(n>0){var r=()=>--n||t();for(var i of e)i.out(r)}else t()}function pi(e,t,n){if((e.f&en)===0){if(e.f^=en,e.transitions!==null)for(const l of e.transitions)(l.is_global||n)&&t.push(l);for(var r=e.first;r!==null;){var i=r.next,o=(r.f&ar)!==0||(r.f&Qt)!==0;pi(r,t,o?n:!1),r=i}}}function Or(e){Oa(e,!0)}function Oa(e,t){if((e.f&en)!==0){e.f^=en,(e.f&ut)===0&&(e.f^=ut),Un(e)&&(St(e,jt),Vr(e));for(var n=e.first;n!==null;){var r=n.next,i=(n.f&ar)!==0||(n.f&Qt)!==0;Oa(n,i?t:!1),n=r}if(e.transitions!==null)for(const o of e.transitions)(o.is_global||t)&&o.in()}}let cr=[],Ci=[];function Ua(){var e=cr;cr=[],ir(e)}function ql(){var e=Ci;Ci=[],ir(e)}function xi(e){cr.length===0&&queueMicrotask(Ua),cr.push(e)}function Vl(){cr.length>0&&Ua(),Ci.length>0&&ql()}let Ur=!1,Wr=!1,zr=null,kn=!1,On=!1;function Wa(e){On=e}let fr=[],Ic=[],Ne=null,Gt=!1;function Yt(e){Ne=e}let Fe=null;function hn(e){Fe=e}let dt=null;function za(e){Ne!==null&&Ne.f&$i&&(dt===null?dt=[e]:dt.push(e))}let ct=null,Dt=0,xt=null;function Xl(e){xt=e}let Ba=1,Br=0,_n=!1;function Ga(){return++Ba}function Un(e){var h;var t=e.f;if((t&jt)!==0)return!0;if((t&dn)!==0){var n=e.deps,r=(t&wt)!==0;if(n!==null){var i,o,l=(t&Ar)!==0,d=r&&Fe!==null&&!_n,s=n.length;if(l||d){var f=e,v=f.parent;for(i=0;i<s;i++)o=n[i],(l||!((h=o==null?void 0:o.reactions)!=null&&h.includes(f)))&&(o.reactions??(o.reactions=[])).push(f);l&&(f.f^=Ar),d&&v!==null&&(v.f&wt)===0&&(f.f^=wt)}for(i=0;i<s;i++)if(o=n[i],Un(o)&&Sa(o),o.wv>e.wv)return!0}(!r||Fe!==null&&!_n)&&St(e,ut)}return!1}function Kl(e,t){for(var n=t;n!==null;){if((n.f&Lr)!==0)try{n.fn(e);return}catch{n.f^=Lr}n=n.parent}throw Ur=!1,e}function Ya(e){return(e.f&Mr)===0&&(e.parent===null||(e.parent.f&Lr)===0)}function Gr(e,t,n,r){if(Ur){if(n===null&&(Ur=!1),Ya(t))throw e;return}if(n!==null&&(Ur=!0),Kl(e,t),Ya(t))throw e}function qa(e,t,n=!0){var r=e.reactions;if(r!==null)for(var i=0;i<r.length;i++){var o=r[i];dt!=null&&dt.includes(e)||((o.f&Ot)!==0?qa(o,t,!1):t===o&&(n?St(o,jt):(o.f&ut)!==0&&St(o,dn),Vr(o)))}}function Va(e){var m;var t=ct,n=Dt,r=xt,i=Ne,o=_n,l=dt,d=Le,s=Gt,f=e.f;ct=null,Dt=0,xt=null,_n=(f&wt)!==0&&(Gt||!kn||Ne===null),Ne=(f&(Qt|Hn))===0?e:null,dt=null,Ta(e.ctx),Gt=!1,Br++,e.f|=$i;try{var v=(0,e.fn)(),h=e.deps;if(ct!==null){var c;if(Yr(e,Dt),h!==null&&Dt>0)for(h.length=Dt+ct.length,c=0;c<ct.length;c++)h[Dt+c]=ct[c];else e.deps=h=ct;if(!_n)for(c=Dt;c<h.length;c++)((m=h[c]).reactions??(m.reactions=[])).push(e)}else h!==null&&Dt<h.length&&(Yr(e,Dt),h.length=Dt);if(Hr()&&xt!==null&&!Gt&&h!==null&&(e.f&(Ot|dn|jt))===0)for(c=0;c<xt.length;c++)qa(xt[c],e);return i!==null&&i!==e&&(Br++,xt!==null&&(r===null?r=xt:r.push(...xt))),v}finally{ct=t,Dt=n,xt=r,Ne=i,_n=o,dt=l,Ta(d),Gt=s,e.f^=$i}}function Zl(e,t){let n=t.reactions;if(n!==null){var r=ol.call(n,e);if(r!==-1){var i=n.length-1;i===0?n=t.reactions=null:(n[r]=n[i],n.pop())}}n===null&&(t.f&Ot)!==0&&(ct===null||!ct.includes(t))&&(St(t,dn),(t.f&(wt|Ar))===0&&(t.f^=Ar),Ca(t),Yr(t,0))}function Yr(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)Zl(e,n[r])}function qr(e){var t=e.f;if((t&Mr)===0){St(e,ut);var n=Fe,r=Le,i=kn;Fe=e,kn=!0;try{(t&yi)!==0?Gl(e):Ia(e),Pa(e);var o=Va(e);e.teardown=typeof o=="function"?o:null,e.wv=Ba;var l=e.deps,d}catch(s){Gr(s,e,n,r||e.ctx)}finally{kn=i,Fe=n}}}function Jl(){try{yl()}catch(e){if(zr!==null)Gr(e,zr,null);else throw e}}function Xa(){var e=kn;try{var t=0;for(kn=!0;fr.length>0;){t++>1e3&&Jl();var n=fr,r=n.length;fr=[];for(var i=0;i<r;i++){var o=jl(n[i]);Ql(o)}or.clear()}}finally{Wr=!1,kn=e,zr=null}}function Ql(e){var t=e.length;if(t!==0)for(var n=0;n<t;n++){var r=e[n];if((r.f&(Mr|en))===0)try{Un(r)&&(qr(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?Na(r):r.fn=null))}catch(i){Gr(i,r,null,r.ctx)}}}function Vr(e){Wr||(Wr=!0,queueMicrotask(Xa));for(var t=zr=e;t.parent!==null;){t=t.parent;var n=t.f;if((n&(Hn|Qt))!==0){if((n&ut)===0)return;t.f^=ut}}fr.push(t)}function jl(e){for(var t=[],n=e;n!==null;){var r=n.f,i=(r&(Qt|Hn))!==0,o=i&&(r&ut)!==0;if(!o&&(r&en)===0){if((r&ba)!==0)t.push(n);else if(i)n.f^=ut;else try{Un(n)&&qr(n)}catch(s){Gr(s,n,null,n.ctx)}var l=n.first;if(l!==null){n=l;continue}}var d=n.parent;for(n=n.next;n===null&&d!==null;)n=d.next,d=d.parent}return t}function es(e){for(var t;;){if(Vl(),fr.length===0)return t;Wr=!0,Xa()}}async function vr(){await Promise.resolve(),es()}function a(e){var t=e.f,n=(t&Ot)!==0;if(Ne!==null&&!Gt){if(!(dt!=null&&dt.includes(e))){var r=Ne.deps;e.rv<Br&&(e.rv=Br,ct===null&&r!==null&&r[Dt]===e?Dt++:ct===null?ct=[e]:(!_n||!ct.includes(e))&&ct.push(e))}}else if(n&&e.deps===null&&e.effects===null){var i=e,o=i.parent;o!==null&&(o.f&wt)===0&&(i.f^=wt)}return n&&(i=e,Un(i)&&Sa(i)),On&&or.has(e)?or.get(e):e.v}function Z(e){var t=Gt;try{return Gt=!0,e()}finally{Gt=t}}const ts=-7169;function St(e,t){e.f=e.f&ts|t}function Ka(e){if(!(typeof e!="object"||!e||e instanceof EventTarget)){if(Dn in e)Si(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&Dn in n&&Si(n)}}}function Si(e,t=new Set){if(typeof e=="object"&&e!==null&&!(e instanceof EventTarget)&&!t.has(e)){t.add(e),e instanceof Date&&e.getTime();for(let r in e)try{Si(e[r],t)}catch{}const n=gi(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=ya(n);for(let i in r){const o=r[i].get;if(o)try{o.call(e)}catch{}}}}}const ns=["touchstart","touchmove"];function rs(e){return ns.includes(e)}function is(e){var t=Ne,n=Fe;Yt(null),hn(null);try{return e()}finally{Yt(t),hn(n)}}const Za=new Set,Ri=new Set;function as(e,t,n,r={}){function i(o){if(r.capture||hr.call(t,o),!o.cancelBubble)return is(()=>n==null?void 0:n.call(this,o))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?xi(()=>{t.addEventListener(e,i,r)}):t.addEventListener(e,i,r),i}function Ve(e,t,n,r,i){var o={capture:r,passive:i},l=as(e,t,n,o);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&Fr(()=>{t.removeEventListener(e,l,o)})}function qt(e){for(var t=0;t<e.length;t++)Za.add(e[t]);for(var n of Ri)n(e)}function hr(e){var R;var t=this,n=t.ownerDocument,r=e.type,i=((R=e.composedPath)==null?void 0:R.call(e))||[],o=i[0]||e.target,l=0,d=e.__root;if(d){var s=i.indexOf(d);if(s!==-1&&(t===document||t===window)){e.__root=t;return}var f=i.indexOf(t);if(f===-1)return;s<=f&&(l=s)}if(o=i[l]||e.target,o!==t){ga(e,"currentTarget",{configurable:!0,get(){return o||n}});var v=Ne,h=Fe;Yt(null),hn(null);try{for(var c,m=[];o!==null;){var _=o.assignedSlot||o.parentNode||o.host||null;try{var y=o["__"+r];if(y!=null&&(!o.disabled||e.target===o))if(Ct(y)){var[g,...$]=y;g.apply(o,[e,...$])}else y.call(o,e)}catch(T){c?m.push(T):c=T}if(e.cancelBubble||_===t||_===null)break;o=_}if(c){for(let T of m)queueMicrotask(()=>{throw T});throw c}}finally{e.__root=t,delete e.currentTarget,Yt(v),hn(h)}}}function os(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function Xr(e,t){var n=Fe;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function W(e,t){var n=(t&Ll)!==0,r=(t&Al)!==0,i,o=!e.startsWith("<!>");return()=>{i===void 0&&(i=os(o?e:"<!>"+e),n||(i=Ir(i)));var l=r||La?document.importNode(i,!0):i.cloneNode(!0);if(n){var d=Ir(l),s=l.lastChild;Xr(d,s)}else Xr(l,l);return l}}function Ja(e=""){{var t=Pr(e+"");return Xr(t,t),t}}function Ce(){var e=document.createDocumentFragment(),t=document.createComment(""),n=Pr();return e.append(t,n),Xr(t,n),e}function L(e,t){e!==null&&e.before(t)}function Qa(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=n,e.nodeValue=n+"")}function ls(e,t){return ss(e,t)}const Wn=new Map;function ss(e,{target:t,anchor:n,props:r={},events:i,context:o,intro:l=!0}){Fl();var d=new Set,s=h=>{for(var c=0;c<h.length;c++){var m=h[c];if(!d.has(m)){d.add(m);var _=rs(m);t.addEventListener(m,hr,{passive:_});var y=Wn.get(m);y===void 0?(document.addEventListener(m,hr,{passive:_}),Wn.set(m,1)):Wn.set(m,y+1)}}};s(mi(Za)),Ri.add(s);var f=void 0,v=Wl(()=>{var h=n??t.appendChild(Pr());return En(()=>{if(o){fe({});var c=Le;c.c=o}i&&(r.$$events=i),f=e(h,r)||{},o&&ve()}),()=>{var _;for(var c of d){t.removeEventListener(c,hr);var m=Wn.get(c);--m===0?(document.removeEventListener(c,hr),Wn.delete(c)):Wn.set(c,m)}Ri.delete(s),h!==n&&((_=h.parentNode)==null||_.removeChild(h))}});return Li.set(f,v),f}let Li=new WeakMap;function us(e,t){const n=Li.get(e);return n?(Li.delete(e),n(t)):Promise.resolve()}function oe(e,t,[n,r]=[0,0]){var i=e,o=null,l=null,d=_t,s=n>0?ar:0,f=!1;const v=(c,m=!0)=>{f=!0,h(m,c)},h=(c,m)=>{d!==(d=c)&&(d?(o?Or(o):m&&(o=En(()=>m(i))),l&&dr(l,()=>{l=null})):(l?Or(l):m&&(l=En(()=>m(i,[n+1,r]))),o&&dr(o,()=>{o=null})))};ur(()=>{f=!1,t(v),f||h(null,null)},s)}function Ie(e,t){return t}function ds(e,t,n,r){for(var i=[],o=t.length,l=0;l<o;l++)pi(t[l].e,i,!0);var d=o>0&&i.length===0&&n!==null;if(d){var s=n.parentNode;Ol(s),s.append(n),r.clear(),mn(e,t[0].prev,t[o-1].next)}Fa(i,()=>{for(var f=0;f<o;f++){var v=t[f];d||(r.delete(v.k),mn(e,v.prev,v.next)),nn(v.e,!d)}})}function we(e,t,n,r,i,o=null){var l=e,d={flags:t,items:new Map,first:null},s=(t&Ea)!==0;if(s){var f=e;l=f.appendChild(Pr())}var v=null,h=!1,c=cn(()=>{var m=n();return Ct(m)?m:m==null?[]:mi(m)});ur(()=>{var m=a(c),_=m.length;h&&_===0||(h=_===0,cs(m,d,l,i,t,r,n),o!==null&&(_===0?v?Or(v):v=En(()=>o(l)):v!==null&&dr(v,()=>{v=null})),a(c))})}function cs(e,t,n,r,i,o,l){var S,A,B,F;var d=(i&kl)!==0,s=(i&(Di|Ei))!==0,f=e.length,v=t.items,h=t.first,c=h,m,_=null,y,g=[],$=[],R,T,b,w;if(d)for(w=0;w<f;w+=1)R=e[w],T=o(R,w),b=v.get(T),b!==void 0&&((S=b.a)==null||S.measure(),(y??(y=new Set)).add(b));for(w=0;w<f;w+=1){if(R=e[w],T=o(R,w),b=v.get(T),b===void 0){var p=c?c.e.nodes_start:n;_=vs(p,t,_,_===null?t.first:_.next,R,T,w,r,i,l),v.set(T,_),g=[],$=[],c=_.next;continue}if(s&&fs(b,R,w,i),(b.e.f&en)!==0&&(Or(b.e),d&&((A=b.a)==null||A.unfix(),(y??(y=new Set)).delete(b))),b!==c){if(m!==void 0&&m.has(b)){if(g.length<$.length){var D=$[0],C;_=D.prev;var x=g[0],H=g[g.length-1];for(C=0;C<g.length;C+=1)ja(g[C],D,n);for(C=0;C<$.length;C+=1)m.delete($[C]);mn(t,x.prev,H.next),mn(t,_,x),mn(t,H,D),c=D,_=H,w-=1,g=[],$=[]}else m.delete(b),ja(b,c,n),mn(t,b.prev,b.next),mn(t,b,_===null?t.first:_.next),mn(t,_,b),_=b;continue}for(g=[],$=[];c!==null&&c.k!==T;)(c.e.f&en)===0&&(m??(m=new Set)).add(c),$.push(c),c=c.next;if(c===null)continue;b=c}g.push(b),_=b,c=b.next}if(c!==null||m!==void 0){for(var I=m===void 0?[]:mi(m);c!==null;)(c.e.f&en)===0&&I.push(c),c=c.next;var N=I.length;if(N>0){var z=(i&Ea)!==0&&f===0?n:null;if(d){for(w=0;w<N;w+=1)(B=I[w].a)==null||B.measure();for(w=0;w<N;w+=1)(F=I[w].a)==null||F.fix()}ds(t,I,z,v)}}d&&xi(()=>{var E;if(y!==void 0)for(b of y)(E=b.a)==null||E.apply()}),Fe.first=t.first&&t.first.e,Fe.last=_&&_.e}function fs(e,t,n,r){(r&Di)!==0&&Ti(e.v,t),(r&Ei)!==0?Ti(e.i,n):e.i=n}function vs(e,t,n,r,i,o,l,d,s,f){var v=(s&Di)!==0,h=(s&Tl)===0,c=v?h?vn(i):fn(i):i,m=(s&Ei)===0?l:fn(l),_={i:m,v:c,k:o,a:null,e:null,prev:n,next:r};try{return _.e=En(()=>d(e,c,m,f),Nl),_.e.prev=n&&n.e,_.e.next=r&&r.e,n===null?t.first=_:(n.next=_,n.e.next=_.e),r!==null&&(r.prev=_,r.e.prev=_.e),_}finally{}}function ja(e,t,n){for(var r=e.next?e.next.e.nodes_start:n,i=t?t.e.nodes_start:n,o=e.e.nodes_start;o!==r;){var l=Nr(o);i.before(o),o=l}}function mn(e,t,n){t===null?e.first=n:(t.next=n,t.e.next=n&&n.e),n!==null&&(n.prev=t,n.e.prev=t&&t.e)}function Tn(e,t,...n){var r=e,i=bt,o;ur(()=>{i!==(i=t())&&(o&&(nn(o),o=null),o=En(()=>i(r,...n)))},ar)}function Ai(e,t,n){var r=e,i,o;ur(()=>{i!==(i=t())&&(o&&(dr(o),o=null),i&&(o=En(()=>n(r,i))))},ar)}function Ue(e,t,n){lr(()=>{var r=Z(()=>t(e,n==null?void 0:n())||{});if(n&&(r!=null&&r.update)){var i=!1,o={};sr(()=>{var l=n();Ka(l),i&&bi(o,l)&&(o=l,r.update(l))}),i=!0}if(r!=null&&r.destroy)return()=>r.destroy()})}function eo(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=eo(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function hs(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=eo(e))&&(r&&(r+=" "),r+=t);return r}function to(e){return typeof e=="object"?hs(e):e??""}function _s(e,t,n){var r=e==null?"":""+e;return r===""?null:r}function no(e,t=!1){var n=t?" !important;":";",r="";for(var i in e){var o=e[i];o!=null&&o!==""&&(r+=" "+i+": "+o+n)}return r}function Mi(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function ms(e,t){if(t){var n="",r,i;if(Array.isArray(t)?(r=t[0],i=t[1]):r=t,e){e=String(e).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var o=!1,l=0,d=!1,s=[];r&&s.push(...Object.keys(r).map(Mi)),i&&s.push(...Object.keys(i).map(Mi));var f=0,v=-1;const y=e.length;for(var h=0;h<y;h++){var c=e[h];if(d?c==="/"&&e[h-1]==="*"&&(d=!1):o?o===c&&(o=!1):c==="/"&&e[h+1]==="*"?d=!0:c==='"'||c==="'"?o=c:c==="("?l++:c===")"&&l--,!d&&o===!1&&l===0){if(c===":"&&v===-1)v=h;else if(c===";"||h===y-1){if(v!==-1){var m=Mi(e.substring(f,v).trim());if(!s.includes(m)){c!==";"&&h++;var _=e.substring(f,h).trim();n+=" "+_+";"}}f=h+1,v=-1}}}}return r&&(n+=no(r)),i&&(n+=no(i,!0)),n=n.trim(),n===""?null:n}return e==null?null:String(e)}function P(e,t,n,r,i,o){var l=e.__className;if(l!==n||l===void 0){var d=_s(n);d==null?e.removeAttribute("class"):e.className=d,e.__className=n}return o}function Hi(e,t={},n,r){for(var i in n){var o=n[i];t[i]!==o&&(n[i]==null?e.style.removeProperty(i):e.style.setProperty(i,o,r))}}function Vt(e,t,n,r){var i=e.__style;if(i!==t){var o=ms(t,r);o==null?e.removeAttribute("style"):e.style.cssText=o,e.__style=t}else r&&(Array.isArray(r)?(Hi(e,n==null?void 0:n[0],r[0]),Hi(e,n==null?void 0:n[1],r[1],"important")):Hi(e,n,r));return r}const gs=Symbol("is custom element"),ys=Symbol("is html");function We(e,t,n,r){var i=$s(e);i[t]!==(i[t]=n)&&(t==="loading"&&(e[hl]=n),n==null?e.removeAttribute(t):typeof n!="string"&&bs(e).includes(t)?e[t]=n:e.setAttribute(t,n))}function $s(e){return e.__attributes??(e.__attributes={[gs]:e.nodeName.includes("-"),[ys]:e.namespaceURI===Ml})}var ro=new Map;function bs(e){var t=ro.get(e.nodeName);if(t)return t;ro.set(e.nodeName,t=[]);for(var n,r=e,i=Element.prototype;i!==r;){n=ya(r);for(var o in n)n[o].set&&t.push(o);r=gi(r)}return t}function gn(e,t,n){var r=wn(e,t);r&&r.set&&(e[t]=n,Fr(()=>{e[t]=null}))}function io(e,t){return e===t||(e==null?void 0:e[Dn])===t}function ze(e={},t,n,r){return lr(()=>{var i,o;return sr(()=>{i=o,o=(r==null?void 0:r())||[],Z(()=>{e!==n(...o)&&(t(e,...o),i&&io(n(...i),e)&&t(null,...i))})}),()=>{xi(()=>{o&&io(n(...o),e)&&t(null,...o)})}}),e}function pn(e=!1){const t=Le,n=t.l.u;if(!n)return;let r=()=>Ka(t.s);if(e){let i=0,o={};const l=In(()=>{let d=!1;const s=t.s;for(const f in s)s[f]!==o[f]&&(o[f]=s[f],d=!0);return d&&i++,i});r=()=>a(l)}n.b.length&&Fn(()=>{ao(t,r),ir(n.b)}),Ze(()=>{const i=Z(()=>n.m.map(ul));return()=>{for(const o of i)typeof o=="function"&&o()}}),n.a.length&&Ze(()=>{ao(t,r),ir(n.a)})}function ao(e,t){if(e.l.s)for(const n of e.l.s)a(n);t()}function Pi(e,t,n){if(e==null)return t(void 0),n&&n(void 0),bt;const r=Z(()=>e.subscribe(t,n));return r.unsubscribe?()=>r.unsubscribe():r}const zn=[];function oo(e,t){return{subscribe:Ye(e,t).subscribe}}function Ye(e,t=bt){let n=null;const r=new Set;function i(d){if(bi(e,d)&&(e=d,n)){const s=!zn.length;for(const f of r)f[1](),zn.push(f,e);if(s){for(let f=0;f<zn.length;f+=2)zn[f][0](zn[f+1]);zn.length=0}}}function o(d){i(d(e))}function l(d,s=bt){const f=[d,s];return r.add(f),r.size===1&&(n=t(i,o)||bt),d(e),()=>{r.delete(f),r.size===0&&n&&(n(),n=null)}}return{set:i,update:o,subscribe:l}}function it(e,t,n){const r=!Array.isArray(e),i=r?[e]:e;if(!i.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=t.length<2;return oo(n,(l,d)=>{let s=!1;const f=[];let v=0,h=bt;const c=()=>{if(v)return;h();const _=t(r?f[0]:f,l,d);o?l(_):h=typeof _=="function"?_:bt},m=i.map((_,y)=>Pi(_,g=>{f[y]=g,v&=~(1<<y),s&&c()},()=>{v|=1<<y}));return s=!0,c(),function(){ir(m),h(),s=!1}})}function _r(e){let t;return Pi(e,n=>t=n)(),t}let Kr=!1,Ii=Symbol();function u(e,t,n){const r=n[t]??(n[t]={store:null,source:vn(void 0),unsubscribe:bt});if(r.store!==e&&!(Ii in n))if(r.unsubscribe(),r.store=e??null,e==null)r.source.v=void 0,r.unsubscribe=bt;else{var i=!0;r.unsubscribe=Pi(e,o=>{i?r.source.v=o:j(r.source,o)}),i=!1}return e&&Ii in n?_r(e):a(r.source)}function de(e,t){return e.set(t),t}function $e(){const e={};function t(){Fr(()=>{for(var n in e)e[n].unsubscribe();ga(e,Ii,{enumerable:!1,value:!0})})}return[e,t]}function Qe(e,t,n){return e.set(n),t}function ws(e){var t=Kr;try{return Kr=!1,[e(),Kr]}finally{Kr=t}}function lo(e){var t;return((t=e.ctx)==null?void 0:t.d)??!1}function Be(e,t,n,r){var p;var i=(n&pl)!==0,o=!Pn||(n&Cl)!==0,l=(n&Sl)!==0,d=(n&Rl)!==0,s=!1,f;l?[f,s]=ws(()=>e[t]):f=e[t];var v=Dn in e||vl in e,h=l&&(((p=wn(e,t))==null?void 0:p.set)??(v&&t in e&&(D=>e[t]=D)))||void 0,c=r,m=!0,_=!1,y=()=>(_=!0,m&&(m=!1,d?c=Z(r):c=r),c);f===void 0&&r!==void 0&&(h&&o&&$l(),f=y(),h&&h(f));var g;if(o)g=()=>{var D=e[t];return D===void 0?y():(m=!0,_=!1,D)};else{var $=(i?In:cn)(()=>e[t]);$.f|=cl,g=()=>{var D=a($);return D!==void 0&&(c=void 0),D===void 0?c:D}}if((n&xl)===0&&o)return g;if(h){var R=e.$$legacy;return function(D,C){return arguments.length>0?((!o||!C||R||s)&&h(C?g():D),D):g()}}var T=!1,b=vn(f),w=In(()=>{var D=g(),C=a(b);return T?(T=!1,C):b.v=D});return l&&a(w),i||(w.equals=wi),function(D,C){if(arguments.length>0){const x=C?a(w):o&&l?tn(D):D;if(!w.equals(x)){if(T=!0,j(b,x),_&&c!==void 0&&(c=x),lo(w))return D;Z(()=>a(w))}return D}return lo(w)?w.v:a(w)}}function Bn(e){Le===null&&ka(),Pn&&Le.l!==null?Ds(Le).m.push(e):Ze(()=>{const t=Z(e);if(typeof t=="function")return t})}function Ds(e){var t=e.l;return t.u??(t.u={a:[],b:[],m:[]})}const Es="5";typeof window<"u"&&((Wo=window.__svelte??(window.__svelte={})).v??(Wo.v=new Set)).add(Es);function Ni(e){j(e,e.v+1)}function Fi(e){return function(t){return t.key==="Enter"||t.key===" "&&!t.preventDefault()?e.call(this,t):void 0}}function K(e,t){let n={update(r){typeof r=="string"?e.innerText=r:r!=null&&r.domNodes?e.replaceChildren(...r.domNodes):r!=null&&r.html&&(e.innerHTML=r.html)}};return n.update(t),n}function Oi(e,t){const n=r=>{e&&!e.contains(r.target)&&e.dispatchEvent(new CustomEvent(t+"outside",{detail:{jsEvent:r}}))};return document.addEventListener(t,n,!0),{destroy(){document.removeEventListener(t,n,!0)}}}function Rt(e,t){let n=new ResizeObserver(r=>{for(let i of r)t(i)});return n.observe(e),{destroy(){n.unobserve(e)}}}const mr=86400;function Xt(e=void 0){return e!==void 0?e instanceof Date?co(e):ps(e):co(new Date)}function tt(e){if(typeof e=="number")e={seconds:e};else if(typeof e=="string"){let n=0,r=2;for(let i of e.split(":",3))n+=parseInt(i,10)*Math.pow(60,r--);e={seconds:n}}else e instanceof Date&&(e={hours:e.getUTCHours(),minutes:e.getUTCMinutes(),seconds:e.getUTCSeconds()});let t=e.weeks||e.week||0;return{years:e.years||e.year||0,months:e.months||e.month||0,days:t*7+(e.days||e.day||0),seconds:(e.hours||e.hour||0)*60*60+(e.minutes||e.minute||0)*60+(e.seconds||e.second||0),inWeeks:!!t}}function J(e){return new Date(e.getTime())}function xe(e,t,n=1){e.setUTCFullYear(e.getUTCFullYear()+n*t.years);let r=e.getUTCMonth()+n*t.months;for(e.setUTCMonth(r),r%=12,r<0&&(r+=12);e.getUTCMonth()!==r;)Cn(e);return e.setUTCDate(e.getUTCDate()+n*t.days),e.setUTCSeconds(e.getUTCSeconds()+n*t.seconds),e}function Zr(e,t,n=1){return xe(e,t,-n)}function ft(e,t=1){return e.setUTCDate(e.getUTCDate()+t),e}function Cn(e,t=1){return ft(e,-t)}function Et(e){return e.setUTCHours(0,0,0,0),e}function je(e){return new Date(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds())}function lt(e,t=19){return e.toISOString().substring(0,t)}function Pe(e,...t){return t.every(n=>e.getTime()===n.getTime())}function ks(e,t){let n=t-e.getUTCDay();return e.setUTCDate(e.getUTCDate()+(n>=0?n:n+7)),e}function so(e,t){let n=t-e.getUTCDay();return e.setUTCDate(e.getUTCDate()+(n<=0?n:n-7)),e}function uo(e){return typeof e=="string"&&e.length<=10}function Ui(e,t){return e.setUTCHours(t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),0),e}function Xe(e){return e.seconds}function Wi(e,t){return xe(e,t),e}function zi(e,t,n){if(Zr(e,t),n.length&&n.length<7)for(;n.includes(e.getUTCDay());)Cn(e);return e}function Ts(e,t){e=J(e),t==0?e.setUTCDate(e.getUTCDate()+6-e.getUTCDay()):e.setUTCDate(e.getUTCDate()+4-(e.getUTCDay()||7));let n=new Date(Date.UTC(e.getUTCFullYear(),0,1));return Math.ceil(((e-n)/1e3/mr+1)/7)}function co(e){return new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds()))}function ps(e){const t=e.match(/\d+/g);return new Date(Date.UTC(Number(t[0]),Number(t[1])-1,Number(t[2]),Number(t[3]||0),Number(t[4]||0),Number(t[5]||0)))}function xn(...e){return Object.assign(...e)}function rn(e){return Object.keys(e)}function Cs(e){return Object.entries(e)}function fo(e){return Math.floor(e)}function vo(e){return Math.ceil(e)}function yn(...e){return Math.min(...e)}function gt(...e){return Math.max(...e)}function xs(){return Symbol("ec")}function Bi(e){return Array.isArray(e)}function Se(e){return typeof e=="function"}function Ss(e){return e()}function ho(e){e.forEach(Ss)}function Rs(){}const Jr=e=>e;function Qr(e){return function(t){t.stopPropagation(),e&&e.call(this,t)}}function _o(e,t,n){n.update(r=>r.set(t,e))}function Ls(e){ho(e),e.clear()}function As(e,t,n){t??(t=e),n.has(t)||n.set(t,setTimeout(()=>{n.delete(t),e()}))}let Gi=xs();function Gn(e,t){e[Gi]=t}function Ms(e){return!!(e!=null&&e[Gi])}function an(e){return e[Gi]}function mo(e,t,n,r=[]){let i=document.createElement(e);i.className=t,typeof n=="string"?i.innerText=n:n.domNodes?i.replaceChildren(...n.domNodes):n.html&&(i.innerHTML=n.html);for(let o of r)i.setAttribute(...o);return i}function Hs(e){return e.scrollHeight>e.clientHeight}function Lt(e){return e.getBoundingClientRect()}function Sn(e,t){for(;t--;)e=e.parentElement;return e}function Yn(e){return Lt(e).height}function qn(e,t,n=document,r=[]){r.push(n);for(let i of n.elementsFromPoint(e,t)){if(Ms(i))return i;if(i.shadowRoot&&!r.includes(i.shadowRoot)){let o=qn(e,t,i.shadowRoot,r);if(o)return o}}return null}function gr(e,t,n,r){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)}function Ps(e,t,n,r){return{type:e,title:t,currentStart:n.start,currentEnd:n.end,activeStart:r.start,activeEnd:r.end,calendar:void 0}}function yt(e){return e=xn({},e),e.currentStart=je(e.currentStart),e.currentEnd=je(e.currentEnd),e.activeStart=je(e.activeStart),e.activeEnd=je(e.activeEnd),e}function go(e){return e.startsWith("list")}function Yi(e){return e.includes("Timeline")}let Is=1;function jr(e){return e.map(t=>{let n={id:"id"in t?String(t.id):`{generated-${Is++}}`,resourceIds:qi(t,"resourceId").map(String),allDay:t.allDay??(uo(t.start)&&uo(t.end)),start:Xt(t.start),end:Xt(t.end),title:t.title??"",editable:t.editable,startEditable:t.startEditable,durationEditable:t.durationEditable,display:t.display??"auto",extendedProps:t.extendedProps??{},backgroundColor:t.backgroundColor??t.color,textColor:t.textColor,classNames:qi(t,"className"),styles:qi(t,"style")};if(n.allDay){Et(n.start);let r=J(n.end);Et(n.end),(!Pe(n.end,r)||Pe(n.end,n.start))&&ft(n.end)}return n})}function qi(e,t){let n=e[t+"s"]??e[t]??[];return Bi(n)?n:[n]}function Ns(e){return e.map(t=>({events:t.events,url:t.url&&t.url.trimEnd("&")||"",method:t.method&&t.method.toUpperCase()||"GET",extraParams:t.extraParams||{}}))}function Kt(e,t,n){let r={start:e.start>t?e.start:t,end:e.end<n?e.end:n,event:e};return r.zeroDuration=Pe(r.start,r.end),r}function ei(e){e.sort((t,n)=>t.start-n.start||n.event.allDay-t.event.allDay)}function Fs(e,t,n,r,i,o){let l=i.formatRange(e.start,t&&e.event.display!=="pointer"&&!e.zeroDuration?Ui(J(e.start),e.end):e.start),d;if(n&&(d=Se(n)?n({event:kt(e.event),timeText:l,view:yt(o)}):n),d===void 0){let s;switch(e.event.display){case"background":s=[];break;case"pointer":s=[yo(l,e,r)];break;default:s=[...e.event.allDay?[]:[yo(l,e,r)],mo("h4",r.eventTitle,e.event.title)]}d={domNodes:s}}return[l,d]}function yo(e,t,n){return mo("time",n.eventTime,e,[["datetime",lt(t.start)]])}function Os(e,t,n){let r=t.classNames;return e&&(Se(e)&&(e=e({event:kt(t),view:yt(n)})),r=[...Bi(e)?e:[e],...r]),r}function kt(e){return $o(e,je)}function Vi(e){return $o(e,J)}function $o(e,t){return e=xn({},e),e.start=t(e.start),e.end=t(e.end),e}function Vn(e,t){let n={};if(e.length){ei(e);let r;for(let i of e){let o=[],l=Et(J(i.start));for(;i.end>l;){if(!t.includes(l.getUTCDay())&&(o.push(J(l)),o.length>1)){let d=l.getTime();n[d]?n[d].chunks.push(i):n[d]={sorted:!1,chunks:[i]}}ft(l)}if(o.length){i.date=o[0],i.days=o.length,i.dates=o,i.start<o[0]&&(i.start=o[0]);let d=ft(J(o.at(-1)));i.end>d&&(i.end=d)}else i.date=Et(J(i.start)),i.days=1,i.dates=[i.date];r&&Pe(r.date,i.date)&&(i.prev=r),r=i}}return n}function bo(e,t,n){e.top=0,e.prev&&(e.top=e.prev.bottom+1),e.bottom=e.top+n;let r=1,i=e.date.getTime();if(t[i]){t[i].sorted||(t[i].chunks.sort((o,l)=>o.top-l.top),t[i].sorted=!0);for(let o of t[i].chunks)if(e.top<o.bottom&&e.bottom>o.top){let l=o.bottom-e.top+1;r+=l,e.top+=l,e.bottom+=l}}return r}function $n(e,t){var r;e.length=t.length;let n=[];for(let i of e)n.push((r=i==null?void 0:i.reposition)==null?void 0:r.call(i));return n}function on(e,t,n,r){return e.start<n&&e.end>t?r?(Bi(r)||(r=[r]),r.some(i=>e.resourceIds.includes(i.id))):!0:!1}function Xn(e){return Us(e)||wo(e)||Ws(e)}function vt(e){return e==="background"}function Us(e){return e==="preview"}function wo(e){return e==="ghost"}function Ws(e){return e==="pointer"}function ti(e){return ri(e,"day")}function ni(e){return ri(e,"week")}function Xi(e){return ri(e,"month")}function zs(e){return ri(e,"year")}function ri(e,t){return{...e,next:"Next "+t,prev:"Previous "+t}}function At(e){return t=>({...t,view:e})}function Bs(e){let t,n;return e&&({start:t,end:n}=e,t&&(t=Et(Xt(t))),n&&(n=Et(Xt(n)))),{start:t,end:n}}function Kn(e,t){return t.start&&e<t.start||t.end&&e>t.end}function Zn(e,t){return t.start&&e<t.start&&(e=t.start),t.end&&e>t.end&&(e=t.end),e}function Do(e){let t=[];return Eo(e,0,t),t}function Eo(e,t,n){let r=[];for(let i of e){let o=Gs(i);r.push(o),n.push(o);let l={level:t,children:[],expanded:!0,hidden:!1};Gn(o,l),i.children&&(l.children=Eo(i.children,t+1,n))}return r}function Gs(e){return{id:String(e.id),title:e.title||"",eventBackgroundColor:e.eventBackgroundColor,eventTextColor:e.eventTextColor,extendedProps:e.extendedProps??{}}}function Ys(e,t){var n;return(n=ko(e,t))==null?void 0:n.eventBackgroundColor}function qs(e,t){var n;return(n=ko(e,t))==null?void 0:n.eventTextColor}function ko(e,t){return t.find(n=>e.resourceIds.includes(n.id))}function Rn(e,t){return it([e,t],([n,r])=>{let i=Se(r)?{format:r}:new Intl.DateTimeFormat(n,r);return{format:o=>i.format(je(o))}})}function To(e,t){return it([e,t],([n,r])=>{let i;if(Se(r))i=r;else{let o=new Intl.DateTimeFormat(n,r);i=(l,d)=>{if(l<=d)return o.formatRange(l,d);{let s=o.formatRangeToParts(d,l),f="",v=["startRange","endRange"],h=[!1,!1];for(let c of s){let m=v.indexOf(c.source);m>=0?h[m]||(f+=Vs(v[1-m],s),h[m]=!0):f+=c.value}return f}}}return{formatRange:(o,l)=>i(je(o),je(l))}})}function Vs(e,t){let n="";for(let r of t)r.source==e&&(n+=r.value);return n}function po(e){return it([e.resources,e.filterResourcesWithEvents,e._filteredEvents,e._activeRange],([t,n,r,i])=>{let o=t.filter(l=>!an(l).hidden);return n&&(o=t.filter(l=>{for(let d of r)if(d.display!=="background"&&d.resourceIds.includes(l.id)&&d.start<i.end&&d.end>i.start)return!0;return!1})),o.length||(o=Do([{}])),o})}function Co(e,t,n,r,i){e=J(e);let o=[],l=J(e);xe(e,r.min),xe(l,r.max),n===void 0&&(n=t.seconds<3600?tt(t.seconds*2):t);let d=J(e);for(;e<l;){for(o.push([lt(e),i.format(e),e>=d]);n.seconds&&e>=d;)xe(d,n);xe(e,t)}return o}function xo(e,t,n,r,i){let o=tt(e),l=tt(t);if(n){let d=tt(yn(Xe(o),gt(0,Xe(l)-mr))),s=tt(gt(Xe(l),Xe(d)+mr)),f=Se(n==null?void 0:n.eventFilter)?n.eventFilter:v=>!vt(v.display);e:for(let v of r){let h=xe(J(v),o),c=xe(J(v),l),m=xe(J(v),d),_=xe(J(v),s);for(let y of i)if(!y.allDay&&f(y)&&y.start<_&&y.end>m){if(y.start<h){let g=gt((y.start-v)/1e3,Xe(d));g<Xe(o)&&(o.seconds=g)}if(y.end>c){let g=yn((y.end-v)/1e3,Xe(s));g>Xe(l)&&(l.seconds=g)}if(Xe(o)===Xe(d)&&Xe(l)===Xe(s))break e}}}return{min:o,max:l}}function Xs(e){var n;let t={allDayContent:void 0,allDaySlot:!0,buttonText:{today:"today"},customButtons:{},date:new Date,datesSet:void 0,dayHeaderFormat:{weekday:"short",month:"numeric",day:"numeric"},dayHeaderAriaLabelFormat:{dateStyle:"full"},displayEventEnd:!0,duration:{weeks:1},events:[],eventAllUpdated:void 0,eventBackgroundColor:void 0,eventClassNames:void 0,eventClick:void 0,eventColor:void 0,eventContent:void 0,eventDidMount:void 0,eventFilter:void 0,eventMouseEnter:void 0,eventMouseLeave:void 0,eventSources:[],eventTextColor:void 0,eventTimeFormat:{hour:"numeric",minute:"2-digit"},filterEventsWithResources:!1,filterResourcesWithEvents:!1,firstDay:0,flexibleSlotTimeLimits:!1,headerToolbar:{start:"title",center:"",end:"today prev,next"},height:void 0,hiddenDays:[],highlightedDates:[],lazyFetching:!0,loading:void 0,locale:void 0,nowIndicator:!1,resourceLabelContent:void 0,resourceLabelDidMount:void 0,resources:[],selectable:!1,scrollTime:"06:00:00",slotDuration:"00:30:00",slotEventOverlap:!0,slotHeight:24,slotLabelInterval:void 0,slotLabelFormat:{hour:"numeric",minute:"2-digit"},slotMaxTime:"24:00:00",slotMinTime:"00:00:00",slotWidth:72,theme:{allDay:"ec-all-day",active:"ec-active",bgEvent:"ec-bg-event",bgEvents:"ec-bg-events",body:"ec-body",button:"ec-button",buttonGroup:"ec-button-group",calendar:"ec",content:"ec-content",day:"ec-day",dayHead:"ec-day-head",days:"ec-days",disabled:"ec-disabled",event:"ec-event",eventBody:"ec-event-body",eventTime:"ec-event-time",eventTitle:"ec-event-title",events:"ec-events",extra:"ec-extra",handle:"ec-handle",header:"ec-header",hiddenScroll:"ec-hidden-scroll",highlight:"ec-highlight",icon:"ec-icon",line:"ec-line",lines:"ec-lines",minor:"ec-minor",nowIndicator:"ec-now-indicator",otherMonth:"ec-other-month",resource:"ec-resource",sidebar:"ec-sidebar",sidebarTitle:"ec-sidebar-title",today:"ec-today",time:"ec-time",title:"ec-title",toolbar:"ec-toolbar",view:"",weekdays:["ec-sun","ec-mon","ec-tue","ec-wed","ec-thu","ec-fri","ec-sat"],withScroll:"ec-with-scroll"},titleFormat:{year:"numeric",month:"short",day:"numeric"},validRange:void 0,view:void 0,viewDidMount:void 0,views:{}};for(let r of e)(n=r.createOptions)==null||n.call(r,t);return t}function Ks(e){var n;let t={date:r=>Et(Xt(r)),duration:tt,events:jr,eventSources:Ns,hiddenDays:r=>[...new Set(r)],highlightedDates:r=>r.map(i=>Et(Xt(i))),resources:Do,scrollTime:tt,slotDuration:tt,slotLabelInterval:r=>r!==void 0?tt(r):void 0,slotMaxTime:tt,slotMinTime:tt,validRange:Bs};for(let r of e)(n=r.createParsers)==null||n.call(r,t);return t}function Zs(e,t){let n=[];for(let r of rn(e))e[r]!==t[r]&&n.push([r,e[r]]);return n}function Js(e){return it(e.view,t=>t==null?void 0:t.startsWith("dayGrid"))}function Qs(e){return it([e._currentRange,e.firstDay,e.slotMaxTime,e._dayGrid],([t,n,r,i])=>{let o=J(t.start),l=J(t.end);if(i)so(o,n),ks(l,n);else if(r.days||r.seconds>mr){xe(Cn(l),r);let d=Cn(J(l));d<o&&(o=d)}return{start:o,end:l}})}function js(e){return it([e.date,e.duration,e.firstDay],([t,n,r])=>{let i=J(t),o;return n.months?i.setUTCDate(1):n.inWeeks&&so(i,r),o=xe(J(i),n),{start:i,end:o}})}function eu(e){return it([e._activeRange,e.hiddenDays],([t,n])=>{let r=[],i=Et(J(t.start)),o=Et(J(t.end));for(;i<o;)n.includes(i.getUTCDay())||r.push(J(i)),ft(i);return!r.length&&n.length&&n.length<7&&(e.date.update(l=>{for(;n.includes(l.getUTCDay());)ft(l);return l}),r=_r(e._viewDates)),r})}function tu(e){return it([e.date,e._activeRange,e._intlTitle,e._dayGrid],([t,n,r,i])=>i?r.formatRange(t,t):r.formatRange(n.start,Cn(J(n.end))))}function nu(e){return it([e.view,e._viewTitle,e._currentRange,e._activeRange],t=>Ps(...t))}function ru(e){let t=Ye([]),n,r=0,i={};return it([e.events,e.eventSources,e._activeRange,e._fetchedRange,e.lazyFetching,e.loading],(o,l)=>_o(()=>{let[d,s,f,v,h,c]=o;if(!s.length){l(d);return}if(!v.start||v.start>f.start||v.end<f.end||!h){n&&n.abort(),n=new AbortController,Se(c)&&!r&&c(!0);let m=()=>{--r===0&&Se(c)&&c(!1)},_=[],y=T=>m(),g=T=>{_=_.concat(jr(T)),l(_),m()},$=lt(f.start),R=lt(f.end);for(let T of s){if(Se(T.events)){let b=T.events({start:je(f.start),end:je(f.end),startStr:$,endStr:R},g,y);b!==void 0&&Promise.resolve(b).then(g,y)}else{let b=Se(T.extraParams)?T.extraParams():xn({},T.extraParams);b.start=$,b.end=R,b=new URLSearchParams(b);let w=T.url,p={},D;["GET","HEAD"].includes(T.method)?w+=(w.includes("?")?"&":"?")+b:(p["content-type"]="application/x-www-form-urlencoded;charset=UTF-8",D=String(b)),fetch(w,{method:T.method,headers:p,body:D,signal:n.signal,credentials:"same-origin"}).then(C=>C.json()).then(g).catch(y)}++r}v.start=f.start,v.end=f.end}},i,e._queue),[]).subscribe(t.set),t}function iu(e){let t;e._view.subscribe(r=>t=r);let n={};return it([e._events,e.eventFilter],(r,i)=>_o(()=>{let[o,l]=r;i(Se(l)?o.filter((d,s,f)=>l({event:d,index:s,events:f,view:t})):o)},n,e._queue),[])}function au(){return oo(Xt(),e=>{let t=setInterval(()=>{e(Xt())},1e3);return()=>clearInterval(t)})}function ou(e){return it(e._now,t=>Et(J(t)))}class lu{constructor(t,n){var l,d;t=t||[];let r=Xs(t),i=Ks(t);r=Ki(r,i),n=Ki(n,i);for(let[s,f]of Object.entries(r))this[s]=Ye(f);this._queue=Ye(new Map),this._tasks=new Map,this._auxiliary=Ye([]),this._dayGrid=Js(this),this._currentRange=js(this),this._activeRange=Qs(this),this._fetchedRange=Ye({start:void 0,end:void 0}),this._events=ru(this),this._now=au(),this._today=ou(this),this._intlEventTime=To(this.locale,this.eventTimeFormat),this._intlSlotLabel=Rn(this.locale,this.slotLabelFormat),this._intlDayHeader=Rn(this.locale,this.dayHeaderFormat),this._intlDayHeaderAL=Rn(this.locale,this.dayHeaderAriaLabelFormat),this._intlTitle=To(this.locale,this.titleFormat),this._bodyEl=Ye(void 0),this._scrollable=Ye(!1),this._recheckScrollable=Ye(!1),this._viewTitle=tu(this),this._viewDates=eu(this),this._view=nu(this),this._viewComponent=Ye(void 0),this._filteredEvents=iu(this),this._interaction=Ye({}),this._iEvents=Ye([null,null]),this._iClasses=Ye(Jr),this._iClass=Ye(void 0),this._set=(s,f)=>{Zi(s,this)&&(i[s]&&(f=i[s](f)),this[s].set(f))},this._get=s=>Zi(s,this)?_r(this[s]):void 0;for(let s of t)(l=s.createStores)==null||l.call(s,this);n.view&&this.view.set(n.view);let o=new Set([...rn(r.views),...rn(n.views??{})]);for(let s of o){let f=So(r,r.views[s]??{}),v=So(f,n,((d=n.views)==null?void 0:d[s])??{}),h=v.component;su(v,this);for(let c of rn(v)){let{set:m,_set:_=m,...y}=this[c];this[c]={set:["buttonText","theme"].includes(c)?g=>{if(Se(g)){let $=g(f[c]);v[c]=$,m(m===_?$:g)}else v[c]=g,m(g)}:g=>{v[c]=g,m(g)},_set:_,...y}}this.view.subscribe(c=>{if(c===s){this._viewComponent.set(h),Se(v.viewDidMount)&&vr().then(()=>v.viewDidMount(_r(this._view)));for(let m of rn(v))this[m]._set(v[m])}})}}}function Ki(e,t){let n={...e};for(let r of rn(t))r in n&&(n[r]=t[r](n[r]));if(e.views){n.views={};for(let r of rn(e.views))n.views[r]=Ki(e.views[r],t)}return n}function So(...e){let t={};for(let n of e){let r={};for(let i of["buttonText","theme"])Se(n[i])&&(r[i]=n[i](t[i]));t={...t,...n,...r}}return t}function su(e,t){rn(e).filter(n=>!Zi(n,t)||n==="view").forEach(n=>delete e[n])}function Zi(e,t){return t.hasOwnProperty(e)&&e[0]!=="_"}El();var uu=W("<h2></h2>"),du=W("<button><i></i></button>"),cu=W("<button><i></i></button>"),fu=W("<button> </button>"),vu=W("<button></button>"),hu=W("<button> </button>");function Ro(e,t){fe(t,!1);const[n,r]=$e(),i=()=>u(x,"$validRange",n),o=()=>u(w,"$date",n),l=()=>u(p,"$duration",n),d=()=>u(D,"$hiddenDays",n),s=()=>u(g,"$_currentRange",n),f=()=>u(R,"$_viewDates",n),v=()=>u(C,"$theme",n),h=()=>u($,"$_viewTitle",n),c=()=>u(T,"$buttonText",n),m=()=>u(b,"$customButtons",n),_=()=>u(H,"$view",n);let y=Be(t,"buttons",8),{_currentRange:g,_viewTitle:$,_viewDates:R,buttonText:T,customButtons:b,date:w,duration:p,hiddenDays:D,theme:C,validRange:x,view:H}=be("state"),I=Et(Xt()),N=vn(),z=vn(),S=vn(),A=vn(!1);function B(){return a(A)}function F(){return f().every(le=>Kn(le,i()))}function E(){de(w,zi(o(),l(),d()))}function O(){de(w,Wi(o(),l()))}zl(()=>(i(),o(),l(),d(),a(S),s(),vr),()=>{if(!B()){if(j(A,!0),j(N,!1),j(z,!1),i().start){let le=J(o());de(w,zi(o(),l(),d())),j(N,F()),de(w,le)}if(i().end){let le=J(o());de(w,Wi(o(),l())),j(z,F()),de(w,le)}if(j(S,I>=s().start&&I<s().end),!a(S)&&(i().start||i().end)){let le=J(o());de(w,J(I)),j(S,F()),de(w,le)}vr().then(()=>j(A,!1))}}),Bl(),pn();var U=Ce(),Q=se(U);we(Q,1,y,Ie,(le,M)=>{var ae=Ce(),_e=se(ae);{var ke=ue=>{var ee=uu();Ue(ee,(De,ge)=>K==null?void 0:K(De,ge),h),q(()=>P(ee,1,v().title)),L(ue,ee)},me=(ue,ee)=>{{var De=Ae=>{var Me=du(),Te=X(Me);q(()=>{P(Me,1,`${v().button??""} ec-${a(M)??""}`),We(Me,"aria-label",c().prev),We(Me,"title",c().prev),Me.disabled=a(N),P(Te,1,`${v().icon??""} ec-${a(M)??""}`)}),Ve("click",Me,E),L(Ae,Me)},ge=(Ae,Me)=>{{var Te=he=>{var Re=cu(),G=X(Re);q(()=>{P(Re,1,`${v().button??""} ec-${a(M)??""}`),We(Re,"aria-label",c().next),We(Re,"title",c().next),Re.disabled=a(z),P(G,1,`${v().icon??""} ec-${a(M)??""}`)}),Ve("click",Re,O),L(he,Re)},te=(he,Re)=>{{var G=pe=>{var Ge=fu(),$t=X(Ge);q(()=>{P(Ge,1,`${v().button??""} ec-${a(M)??""}`),Ge.disabled=a(S),Qa($t,c()[a(M)])}),Ve("click",Ge,()=>de(w,J(I))),L(pe,Ge)},re=(pe,Ge)=>{{var $t=Mt=>{var Ke=vu();lr(()=>Ve("click",Ke,function(...Ht){var Pt;(Pt=m()[a(M)].click)==null||Pt.apply(this,Ht)})),Ue(Ke,(Ht,Pt)=>K==null?void 0:K(Ht,Pt),()=>m()[a(M)].text),q(()=>P(Ke,1,`${v().button??""} ec-${a(M)??""}${m()[a(M)].active?" "+v().active:""}`)),L(Mt,Ke)},ln=(Mt,Ke)=>{{var Ht=Pt=>{var sn=hu(),br=X(sn);q(()=>{P(sn,1,`${v().button??""}${_()===a(M)?" "+v().active:""} ec-${a(M)??""}`),Qa(br,c()[a(M)])}),Ve("click",sn,()=>de(H,a(M))),L(Pt,sn)};oe(Mt,Pt=>{a(M)!=""&&Pt(Ht)},Ke)}};oe(pe,Mt=>{m()[a(M)]?Mt($t):Mt(ln,!1)},Ge)}};oe(he,pe=>{a(M)=="today"?pe(G):pe(re,!1)},Re)}};oe(Ae,he=>{a(M)=="next"?he(Te):he(te,!1)},Me)}};oe(ue,Ae=>{a(M)=="prev"?Ae(De):Ae(ge,!1)},ee)}};oe(_e,ue=>{a(M)=="title"?ue(ke):ue(me,!1)})}L(le,ae)}),L(e,U),ve(),r()}var _u=W("<div><!></div>"),mu=W("<div></div>"),gu=W("<nav></nav>");function yu(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(l,"$headerToolbar",n),o=()=>u(d,"$theme",n);let{headerToolbar:l,theme:d}=be("state"),s=k(()=>{var h;let v={};for(let c of["start","center","end"])v[c]=((h=i()[c])==null?void 0:h.split(" ").map(m=>m.split(",")))??[];return v});var f=gu();we(f,21,()=>rn(a(s)),Ie,(v,h)=>{var c=mu();we(c,21,()=>a(s)[a(h)],Ie,(m,_)=>{var y=Ce(),g=se(y);{var $=T=>{var b=_u(),w=X(b);Ro(w,{get buttons(){return a(_)}}),q(()=>P(b,1,o().buttonGroup)),L(T,b)},R=T=>{Ro(T,{get buttons(){return a(_)}})};oe(g,T=>{a(_).length>1?T($):T(R,!1)})}L(m,y)}),q(()=>P(c,1,`ec-${a(h)??""}`)),L(v,c)}),q(()=>P(f,1,o().toolbar)),L(e,f),ve(),r()}function $u(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(g,"$_activeRange",n),o=()=>u(m,"$datesSet",n),l=()=>u(D,"$_view",n),d=()=>u($,"$_filteredEvents",n),s=()=>u(_,"$eventAllUpdated",n),f=()=>u(p,"$_queue",n),v=()=>u(w,"$_recheckScrollable",n),h=()=>u(T,"$_bodyEl",n),c=()=>u(y,"$_auxiliary",n);let{datesSet:m,eventAllUpdated:_,_auxiliary:y,_activeRange:g,_filteredEvents:$,_scrollable:R,_bodyEl:T,_tasks:b,_recheckScrollable:w,_queue:p,_view:D}=be("state");Ze(()=>{i(),Z(()=>{Se(o())&&o()({start:je(i().start),end:je(i().end),startStr:lt(i().start),endStr:lt(i().end),view:yt(l())})})}),Ze(()=>{d(),Z(()=>{Se(s())&&As(()=>s()({view:yt(l())}),"eau",b)})}),Ze(()=>{f(),Z(()=>{Ls(f())})}),Ze(()=>{v(),Z(()=>{h()&&de(R,Hs(h())),de(w,!1)})});var C=Ce(),x=se(C);we(x,1,c,Ie,(H,I)=>{var N=Ce(),z=se(N);Ai(z,()=>a(I),(S,A)=>{A(S,{})}),L(H,N)}),L(e,C),ve(),r()}var bu=W("<div><!> <!></div> <!>",1);function wu(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(w,"$_events",n),o=()=>u(T,"$_interaction",n),l=()=>u(D,"$date",n),d=()=>u(C,"$duration",n),s=()=>u(x,"$hiddenDays",n),f=()=>u(R,"$_viewComponent",n),v=()=>u(I,"$theme",n),h=()=>u(p,"$_scrollable",n),c=()=>u(b,"$_iClass",n),m=()=>u(H,"$height",n),_=()=>u(N,"$view",n);let y=Be(t,"plugins",19,()=>[]),g=Be(t,"options",19,()=>({})),$=new lu(y(),g());Hl("state",$);let{_viewComponent:R,_interaction:T,_iClass:b,_events:w,_scrollable:p,date:D,duration:C,hiddenDays:x,height:H,theme:I,view:N}=$,z={...g()};Ze(()=>{for(let[te,he]of Zs(g(),z))Z(()=>{S(te,he)});xn(z,g())});function S(te,he){return $._set(te,he),this}function A(te){let he=$._get(te);return he instanceof Date?je(he):he}function B(){return $._fetchedRange.set({start:void 0,end:void 0}),this}function F(){return i().map(kt)}function E(te){for(let he of i())if(he.id==te)return kt(he);return null}function O(te){return te=jr([te])[0],i().push(te),de(w,i()),kt(te)}function U(te){let he=String(te.id),Re=i().findIndex(G=>G.id===he);return Re>=0?(Qe(w,Z(i)[Re]=jr([te])[0],Z(i)),kt(te)):null}function Q(te){te=String(te);let he=i().findIndex(Re=>Re.id===te);return he>=0&&(i().splice(he,1),de(w,i())),this}function le(){return yt(_r($._view))}function M(){var te;return(te=o().action)==null||te.unselect(),this}function ae(te,he){let Re=qn(te,he);if(Re){let G=an(Re)(te,he);return G.date=je(G.date),G}return null}function _e(){return de(D,Wi(l(),d())),this}function ke(){return de(D,zi(l(),d(),s())),this}let me=k(f);var ue=bu(),ee=se(ue);let De;var ge=X(ee);yu(ge,{});var Ae=ie(ge,2);Ai(Ae,()=>a(me),(te,he)=>{he(te,{})});var Me=ie(ee,2);$u(Me,{}),q(te=>{P(ee,1,`${v().calendar??""} ${v().view??""}${h()?" "+v().withScroll:""}${c()?" "+v()[c()]:""}`),We(ee,"role",te),De=Vt(ee,"",De,{height:m()})},[()=>go(_())?"list":"table"]),L(e,ue);var Te=ve({setOption:S,getOption:A,refetchEvents:B,getEvents:F,getEventById:E,addEvent:O,updateEvent:U,removeEventById:Q,getView:le,unselect:M,dateFromPoint:ae,next:_e,prev:ke});return r(),Te}function Du(e){return it([e.date,e.firstDay,e.hiddenDays],([t,n,r])=>{let i=[],o=J(t),l=7;for(;o.getUTCDay()!==n&&l;)Cn(o),--l;for(let d=0;d<7;++d)r.includes(o.getUTCDay())||i.push(J(o)),ft(o);return i})}var Eu=W('<div role="columnheader"><span></span></div>'),ku=W('<div><div role="row"></div> <div></div></div>');function Tu(e,t){fe(t,!1);const[n,r]=$e(),i=()=>u(s,"$theme",n),o=()=>u(h,"$_days",n),l=()=>u(v,"$_intlDayHeaderAL",n),d=()=>u(f,"$_intlDayHeader",n);let{theme:s,_intlDayHeader:f,_intlDayHeaderAL:v,_days:h}=be("state");pn();var c=ku(),m=X(c);we(m,5,o,Ie,(y,g)=>{var $=Eu(),R=X($);Ue(R,(T,b)=>K==null?void 0:K(T,b),()=>d().format(a(g))),q((T,b)=>{P($,1,`${i().day??""} ${T??""}`),We(R,"aria-label",b)},[()=>{var T;return(T=i().weekdays)==null?void 0:T[a(g).getUTCDay()]},()=>l().format(a(g))],cn),L(y,$)});var _=ie(m,2);q(()=>{P(c,1,i().header),P(m,1,i().days),P(_,1,i().hiddenScroll)}),L(e,c),ve(),r()}var pu=["forEach","isDisjointFrom","isSubsetOf","isSupersetOf"],Cu=["difference","intersection","symmetricDifference","union"],Lo=!1;const ui=class ui extends Set{constructor(n){super();Sr(this,si);Sr(this,Qn,new Map);Sr(this,Ut,fn(0));Sr(this,bn,fn(0));if(n){for(var r of n)super.add(r);st(this,bn).v=super.size}Lo||il(this,si,al).call(this)}has(n){var r=super.has(n),i=st(this,Qn),o=i.get(n);if(o===void 0){if(!r)return a(st(this,Ut)),!1;o=fn(!0),i.set(n,o)}return a(o),r}add(n){return super.has(n)||(super.add(n),j(st(this,bn),super.size),Ni(st(this,Ut))),this}delete(n){var r=super.delete(n),i=st(this,Qn),o=i.get(n);return o!==void 0&&(i.delete(n),j(o,!1)),r&&(j(st(this,bn),super.size),Ni(st(this,Ut))),r}clear(){if(super.size!==0){super.clear();var n=st(this,Qn);for(var r of n.values())j(r,!1);n.clear(),j(st(this,bn),0),Ni(st(this,Ut))}}keys(){return this.values()}values(){return a(st(this,Ut)),super.values()}entries(){return a(st(this,Ut)),super.entries()}[Symbol.iterator](){return this.keys()}get size(){return a(st(this,bn))}};Qn=new WeakMap,Ut=new WeakMap,bn=new WeakMap,si=new WeakSet,al=function(){Lo=!0;var n=ui.prototype,r=Set.prototype;for(const i of pu)n[i]=function(...o){return a(st(this,Ut)),r[i].apply(this,o)};for(const i of Cu)n[i]=function(...o){a(st(this,Ut));var l=r[i].apply(this,o);return new ui(l)}};let Ji=ui;var xu=W("<div></div>"),Su=W("<article><!></article>");function Ao(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(A,"$resources",n),o=()=>u(p,"$eventBackgroundColor",n),l=()=>u(D,"$eventColor",n),d=()=>u(S,"$eventTextColor",n),s=()=>u(B,"$theme",n),f=()=>u(I,"$eventClassNames",n),v=()=>u(F,"$_view",n),h=()=>u(w,"$displayEventEnd",n),c=()=>u(C,"$eventContent",n),m=()=>u(E,"$_intlEventTime",n),_=()=>u(H,"$eventDidMount",n),y=()=>u(x,"$eventClick",n),g=()=>u(N,"$eventMouseEnter",n),$=()=>u(z,"$eventMouseLeave",n);let R=Be(t,"el",15),T=Be(t,"classes",3,Jr),b=Be(t,"styles",3,Jr),{displayEventEnd:w,eventBackgroundColor:p,eventColor:D,eventContent:C,eventClick:x,eventDidMount:H,eventClassNames:I,eventMouseEnter:N,eventMouseLeave:z,eventTextColor:S,resources:A,theme:B,_view:F,_intlEventTime:E}=be("state"),O=k(()=>t.chunk.event),U=k(()=>t.chunk.event.display),Q=k(()=>a(O).backgroundColor??Ys(a(O),i())??o()??l()),le=k(()=>a(O).textColor??qs(a(O),i())??d()),M=k(()=>Cs(b()({"background-color":a(Q),color:a(le)})).map(G=>`${G[0]}:${G[1]}`).join(";")),ae=k(()=>T()([vt(a(U))?s().bgEvent:s().event,...Os(f(),a(O),v())]).join(" ")),_e=k(()=>Fs(t.chunk,h(),c(),s(),m(),v())),ke=k(()=>Mn(a(_e),2)),me=k(()=>a(ke)[0]),ue=k(()=>a(ke)[1]);Bn(()=>{Se(_())&&_()({event:kt(a(O)),timeText:a(me),el:R(),view:yt(v())})});function ee(G,re){return!Xn(re)&&Se(G)?pe=>G({event:kt(a(O)),el:R(),jsEvent:pe,view:yt(v())}):void 0}let De=k(()=>!vt(a(U))&&ee(y(),a(U))||void 0),ge=k(()=>a(De)&&Fi(a(De))),Ae=k(()=>ee(g(),a(U))),Me=k(()=>ee($(),a(U)));var Te=Su();Te.__click=function(...G){var re;(re=a(De))==null||re.apply(this,G)},Te.__keydown=function(...G){var re;(re=a(ge))==null||re.apply(this,G)},Te.__pointerdown=function(...G){var re;(re=t.onpointerdown)==null||re.apply(this,G)};{const G=re=>{var pe=xu();Ue(pe,(Ge,$t)=>K==null?void 0:K(Ge,$t),()=>a(ue)),q(()=>P(pe,1,to(s().eventBody))),L(re,pe)};var te=X(Te);{var he=re=>{var pe=Ce(),Ge=se(pe);Tn(Ge,()=>t.body,()=>G,()=>a(Q),()=>a(le)),L(re,pe)},Re=re=>{G(re)};oe(te,re=>{t.body?re(he):re(Re,!1)})}ze(Te,re=>R(re),()=>R())}q(()=>{P(Te,1,to(a(ae))),Vt(Te,a(M)),We(Te,"role",a(De)?"button":void 0),We(Te,"tabindex",a(De)?0:void 0)}),Ve("mouseenter",Te,function(...G){var re;(re=a(Ae))==null||re.apply(this,G)}),Ve("mouseleave",Te,function(...G){var re;(re=a(Me))==null||re.apply(this,G)}),L(e,Te),ve(),r()}qt(["click","keydown","pointerdown"]);function ii(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(s,"$_iClasses",n),o=()=>u(d,"$_interaction",n);let l=Be(t,"el",15),{_interaction:d,_iClasses:s}=be("state"),f=k(()=>t.chunk.event),v=k(()=>t.chunk.event.display),h=k(()=>y=>i()(y,a(f)));function c(y){var g,$;return(g=o().action)!=null&&g.draggable(y)?R=>{var T,b;return o().action.drag(y,R,(T=t.forceDate)==null?void 0:T.call(t),(b=t.forceMargin)==null?void 0:b.call(t))}:($=o().action)==null?void 0:$.noAction}let m=k(()=>!vt(a(v))&&!Xn(a(v))?c(a(f)):void 0),_=k(()=>o().resizer);Ao(e,{get chunk(){return t.chunk},get classes(){return a(h)},get styles(){return t.styles},get onpointerdown(){return a(m)},get el(){return l()},set el(g){l(g)},body:(g,$=bt)=>{var R=Ce(),T=se(R);{var b=p=>{var D=Ce(),C=se(D);Ai(C,()=>a(_),(x,H)=>{H(x,{get chunk(){return t.chunk},get axis(){return t.axis},get forceDate(){return t.forceDate},get forceMargin(){return t.forceMargin},children:(I,N)=>{var z=Ce(),S=se(z);Tn(S,$),L(I,z)},$$slots:{default:!0}})}),L(p,D)},w=p=>{var D=Ce(),C=se(D);Tn(C,$),L(p,D)};oe(T,p=>{a(_)?p(b):p(w,!1)})}L(g,R)},$$slots:{body:!0}}),ve(),r()}function yr(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(v,"$dayMaxEvents",n),o=()=>u(h,"$_hiddenEvents",n),l=()=>u(c,"$_popupDate",n);let d=Be(t,"longChunks",19,()=>({})),s=Be(t,"inPopup",3,!1),f=Be(t,"dates",19,()=>[]),{dayMaxEvents:v,_hiddenEvents:h,_popupDate:c}=be("state"),m=He(void 0),_=He(1),y=He(!1),g=k(()=>t.chunk.event),$=k(()=>t.chunk.event.display),R=k(()=>D=>{if(vt(a($)))D.width=`calc(${t.chunk.days*100}% + ${t.chunk.days-1}px)`;else{let C=a(_);if(a(g)._margin){let[x,H]=a(g)._margin;t.chunk.date>=H[0]&&t.chunk.date<=H.at(-1)&&(C=x)}D.width=`calc(${t.chunk.days*100}% + ${(t.chunk.days-1)*7}px)`,D["margin-top"]=`${C}px`}return a(y)&&(D.visibility="hidden"),D});function T(){j(_,bo(t.chunk,d(),Yn(a(m))),!0),i()===!0?b():j(y,!1)}function b(){let D=Sn(a(m),2),C=Yn(D)-Yn(D.firstElementChild)-w(D);j(y,t.chunk.bottom>C);let x=!1;for(let H of t.chunk.dates){let I=o()[H.getTime()];if(I){let N=I.size;a(y)?I.add(t.chunk.event):I.delete(t.chunk.event),N!==I.size&&(x=!0)}}x&&de(h,o())}function w(D){let C=0;for(let x=0;x<t.chunk.days&&(C=gt(C,Yn(D.lastElementChild)),D=D.nextElementSibling,!!D);++x);return C}ii(e,{get chunk(){return t.chunk},get styles(){return a(R)},axis:"x",forceDate:()=>s()?l():void 0,forceMargin:()=>[Lt(a(m)).top-Lt(Sn(a(m),1)).top,f()],get el(){return a(m)},set el(D){j(m,D,!0)}});var p=ve({reposition:T});return r(),p}var Ru=W('<div><div><time></time> <a role="button" tabindex="0">&times;</a></div> <div></div></div>');function Lu(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(y,"$_popupChunks",n),o=()=>u(_,"$_popupDate",n),l=()=>u(c,"$_interaction",n),d=()=>u(h,"$theme",n),s=()=>u(m,"$_intlDayPopover",n),f=()=>u(v,"$buttonText",n);let{buttonText:v,theme:h,_interaction:c,_intlDayPopover:m,_popupDate:_,_popupChunks:y}=be("state"),g=He(void 0),$=He("");function R(){let S=Sn(a(g),1),A=Sn(S,3),B=Lt(a(g)),F=Lt(S),E=Lt(A);j($,"");let O;if(B.width>=E.width){O=E.left-F.left;let Q=F.right-E.right;j($,a($)+`right:${Q}px;`)}else O=(F.width-B.width)/2,F.left+O<E.left?O=E.left-F.left:F.left+O+B.width>E.right&&(O=E.right-F.left-B.width);j($,a($)+`left:${O}px;`);let U;if(B.height>=E.height){U=E.top-F.top;let Q=F.bottom-E.bottom;j($,a($)+`bottom:${Q}px;`)}else U=(F.height-B.height)/2,F.top+U<E.top?U=E.top-F.top:F.top+U+B.height>E.bottom&&(U=E.bottom-F.top-B.height);j($,a($)+`top:${U}px;`)}Ze(()=>{i(),vr().then(T)});function T(){i().length?R():b()}function b(){de(_,null),de(y,[])}function w(){var S;b(),(S=l().action)==null||S.noClick()}var p=Ru(),D=k(Qr);p.__pointerdown=function(...S){var A;(A=a(D))==null||A.apply(this,S)};var C=X(p),x=X(C);Ue(x,(S,A)=>K==null?void 0:K(S,A),()=>s().format(o()));var H=ie(x,2),I=k(()=>Qr(b));H.__click=function(...S){var A;(A=a(I))==null||A.apply(this,S)};var N=k(()=>Fi(b));H.__keydown=function(...S){var A;(A=a(N))==null||A.apply(this,S)};var z=ie(C,2);we(z,5,i,S=>S.event,(S,A)=>{yr(S,{get chunk(){return a(A)},inPopup:!0})}),ze(p,S=>j(g,S),()=>a(g)),Ue(p,(S,A)=>Oi==null?void 0:Oi(S,A),()=>"pointerdown"),q(S=>{P(p,1,d().popup),Vt(p,a($)),P(C,1,d().dayHead),We(x,"datetime",S),We(H,"aria-label",f().close),P(z,1,d().events)},[()=>lt(o(),10)]),Ve("pointerdownoutside",p,w),L(e,p),ve(),r()}qt(["pointerdown","click","keydown"]);var Au=W("<span></span>"),Mu=W("<div><!></div>"),Hu=W("<div><!></div>"),Pu=W("<!> <!>",1),Iu=W('<a role="button" tabindex="0" aria-haspopup="true"></a>'),Nu=W('<div role="cell"><div><time></time> <!></div> <div><!></div> <!> <div><!></div> <!> <div><!></div></div>');function Fu(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(I,"$_hiddenEvents",n),o=()=>u(A,"$_today",n),l=()=>u(R,"$currentDate",n),d=()=>u(b,"$highlightedDates",n),s=()=>u(C,"$validRange",n),f=()=>u(p,"$moreLinkContent",n),v=()=>u(z,"$_popupDate",n),h=()=>u(x,"$weekNumbers",n),c=()=>u(w,"$firstDay",n),m=()=>u(H,"$weekNumberContent",n),_=()=>u(D,"$theme",n),y=()=>u(B,"$_interaction",n),g=()=>u(N,"$_intlDayCell",n);let $=Be(t,"iChunks",19,()=>[]),{date:R,dayMaxEvents:T,highlightedDates:b,firstDay:w,moreLinkContent:p,theme:D,validRange:C,weekNumbers:x,weekNumberContent:H,_hiddenEvents:I,_intlDayCell:N,_popupDate:z,_popupChunks:S,_today:A,_interaction:B}=be("state"),F=He(void 0),E=new Ji;Fn(()=>{Qe(I,Z(i)[t.date.getTime()]=Z(()=>E),Z(i))});let O=[],U=k(()=>Pe(t.date,o())),Q=k(()=>t.date.getUTCMonth()!==l().getUTCMonth()),le=k(()=>d().some(ne=>Pe(ne,t.date))),M=k(()=>Kn(t.date,s())),ae=k(()=>a(M)?[]:t.bgChunks.filter(ne=>Pe(ne.date,t.date))),_e=k(()=>{let ne=[];if(!a(M))for(let Ee of t.chunks)Pe(Ee.date,t.date)&&ne.push(Ee);return ne});Fn(()=>{a(_e),E.clear()});let ke=k(()=>{let ne="";if(!a(M)&&E.size){let Ee="+"+E.size+" more";f()?ne=Se(f())?f()({num:E.size,text:Ee}):f():ne=Ee}return ne});Bn(()=>{Gn(a(F),()=>({allDay:!0,date:t.date,resource:void 0,dayEl:a(F),disabled:a(M)}))});function me(){de(z,t.date)}let ue=k(()=>v()&&Pe(t.date,v()));Fn(()=>{a(_e),t.longChunks,a(ue)&&vr().then(ee)});function ee(){var at;let ne=ft(J(t.date)),Ee=a(_e).concat(((at=t.longChunks[t.date.getTime()])==null?void 0:at.chunks)||[]);de(S,Ee.map(et=>xn({},et,Kt(et.event,t.date,ne),{days:1,dates:[t.date]})).sort((et,Wt)=>et.top-Wt.top))}let De=k(()=>h()&&t.date.getUTCDay()==(c()?1:0)),ge=k(()=>{let ne;if(a(De)){let Ee=Ts(t.date,c());m()?ne=Se(m())?m()({date:je(t.date),week:Ee}):m():ne="W"+String(Ee).padStart(2,"0")}return ne});function Ae(){a(M)||$n(O,a(_e))}var Me=Nu();Me.__pointerdown=function(...ne){var Ee,at;(at=(Ee=y().action)==null?void 0:Ee.select)==null||at.apply(this,ne)};var Te=X(Me),te=X(Te);Ue(te,(ne,Ee)=>K==null?void 0:K(ne,Ee),()=>g().format(t.date));var he=ie(te,2);{var Re=ne=>{var Ee=Au();Ue(Ee,(at,et)=>K==null?void 0:K(at,et),()=>a(ge)),q(()=>P(Ee,1,_().weekNumber)),L(ne,Ee)};oe(he,ne=>{a(De)&&ne(Re)})}var G=ie(Te,2),re=X(G);{var pe=ne=>{var Ee=Ce(),at=se(Ee);we(at,17,()=>a(ae),et=>et.event,(et,Wt)=>{yr(et,{get chunk(){return a(Wt)}})}),L(ne,Ee)};oe(re,ne=>{a(M)||ne(pe)})}var Ge=ie(G,2);{var $t=ne=>{var Ee=Pu(),at=se(Ee);{var et=qe=>{var It=Mu(),jn=X(It);yr(jn,{get chunk(){return $()[2]}}),q(()=>P(It,1,_().events)),L(qe,It)};oe(at,qe=>{$()[2]&&Pe($()[2].date,t.date)&&qe(et)})}var Wt=ie(at,2);{var Tt=qe=>{var It=Hu(),jn=X(It);yr(jn,{get chunk(){return $()[0]}}),q(()=>P(It,1,`${_().events??""} ${_().preview??""}`)),L(qe,It)};oe(Wt,qe=>{$()[0]&&Pe($()[0].date,t.date)&&qe(Tt)})}L(ne,Ee)};oe(Ge,ne=>{a(M)||ne($t)})}var ln=ie(Ge,2),Mt=X(ln);{var Ke=ne=>{var Ee=Ce(),at=se(Ee);we(at,19,()=>a(_e),et=>et.event,(et,Wt,Tt)=>{ze(yr(et,{get chunk(){return a(Wt)},get longChunks(){return t.longChunks},get dates(){return t.dates}}),(qe,It)=>O[It]=qe,qe=>O==null?void 0:O[qe],()=>[a(Tt)])}),L(ne,Ee)};oe(Mt,ne=>{a(M)||ne(Ke)})}var Ht=ie(ln,2);{var Pt=ne=>{Lu(ne,{})};oe(Ht,ne=>{a(ue)&&ne(Pt)})}var sn=ie(Ht,2),br=X(sn);{var ra=ne=>{var Ee=Iu(),at=k(()=>Qr(me));Ee.__click=function(...Tt){var qe;(qe=a(at))==null||qe.apply(this,Tt)};var et=k(()=>Fi(me));Ee.__keydown=function(...Tt){var qe;(qe=a(et))==null||qe.apply(this,Tt)};var Wt=k(Qr);Ee.__pointerdown=function(...Tt){var qe;(qe=a(Wt))==null||qe.apply(this,Tt)},Ue(Ee,(Tt,qe)=>K==null?void 0:K(Tt,qe),()=>a(ke)),L(ne,Ee)};oe(br,ne=>{!a(M)&&E.size&&ne(ra)})}ze(Me,ne=>j(F,ne),()=>a(F)),q((ne,Ee)=>{P(Me,1,`${_().day??""} ${ne??""}${a(U)?" "+_().today:""}${a(Q)?" "+_().otherMonth:""}${a(le)?" "+_().highlight:""}${a(M)?" "+_().disabled:""}`),P(Te,1,_().dayHead),We(te,"datetime",Ee),P(G,1,_().bgEvents),P(ln,1,_().events),P(sn,1,_().dayFoot)},[()=>{var ne;return(ne=_().weekdays)==null?void 0:ne[t.date.getUTCDay()]},()=>lt(t.date,10)]),L(e,Me);var ia=ve({reposition:Ae});return r(),ia}qt(["pointerdown","click","keydown"]);var Ou=W('<div role="row"></div>');function Uu(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u($,"$validRange",n),o=()=>u(h,"$_filteredEvents",n),l=()=>u(_,"$filterEventsWithResources",n),d=()=>u(m,"$resources",n),s=()=>u(y,"$hiddenDays",n),f=()=>u(c,"$_iEvents",n),v=()=>u(g,"$theme",n);let{_filteredEvents:h,_iEvents:c,resources:m,filterEventsWithResources:_,hiddenDays:y,theme:g,validRange:$}=be("state"),R=[],T=k(()=>Zn(t.dates[0],i())),b=k(()=>ft(J(Zn(t.dates.at(-1),i())))),w=k(()=>{let S=[],A=[];for(let F of o())if(on(F,a(T),a(b),l()?d():void 0)){let E=Kt(F,a(T),a(b));vt(F.display)?F.allDay&&A.push(E):S.push(E)}Vn(A,s());let B=Vn(S,s());return[S,A,B]}),p=k(()=>Mn(a(w),3)),D=k(()=>a(p)[0]),C=k(()=>a(p)[1]),x=k(()=>a(p)[2]),H=k(()=>f().map(S=>{let A;return S&&on(S,a(T),a(b))?(A=Kt(S,a(T),a(b)),Vn([A],s())):A=null,A}));function I(){$n(R,t.dates)}var N=Ou();we(N,21,()=>t.dates,Ie,(S,A,B)=>{ze(Fu(S,{get date(){return a(A)},get chunks(){return a(D)},get bgChunks(){return a(C)},get longChunks(){return a(x)},get iChunks(){return a(H)},get dates(){return t.dates}}),(F,E)=>R[E]=F,F=>R==null?void 0:R[F],()=>[B])}),q(()=>P(N,1,v().days)),L(e,N);var z=ve({reposition:I});return r(),z}var Wu=W("<div><div></div></div>");function zu(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u($,"$hiddenDays",n),o=()=>u(c,"$_viewDates",n),l=()=>u(g,"$dayMaxEvents",n),d=()=>u(_,"$_hiddenEvents",n),s=()=>u(m,"$_filteredEvents",n),f=()=>u(h,"$_bodyEl",n),v=()=>u(R,"$theme",n);let{_bodyEl:h,_viewDates:c,_filteredEvents:m,_hiddenEvents:_,_recheckScrollable:y,dayMaxEvents:g,hiddenDays:$,theme:R}=be("state"),T=[],b=k(()=>7-i().length),w=k(()=>{let x=[];for(let H=0;H<o().length/a(b);++H){let I=[];for(let N=0;N<a(b);++N)I.push(o()[H*a(b)+N]);x.push(I)}return x});Fn(()=>{a(w),l(),de(_,{})});function p(){$n(T,a(w))}Ze(()=>{s(),d(),l(),Z(p)});var D=Wu();Ve("resize",mt,p);var C=X(D);we(C,21,()=>a(w),Ie,(x,H,I)=>{ze(Uu(x,{get dates(){return a(H)}}),(N,z)=>T[z]=N,N=>T==null?void 0:T[N],()=>[I])}),ze(D,x=>de(h,x),()=>f()),Ue(D,(x,H)=>Rt==null?void 0:Rt(x,H),()=>()=>de(y,!0)),q(()=>{P(D,1,`${v().body??""}${l()===!0?" "+v().uniform:""}`),P(C,1,v().content)}),L(e,D),ve(),r()}var Bu=W("<!> <!>",1);function Gu(e){var t=Bu(),n=se(t);Tu(n,{});var r=ie(n,2);zu(r,{}),L(e,t)}const Yu={createOptions(e){e.dayMaxEvents=!1,e.dayCellFormat={day:"numeric"},e.dayPopoverFormat={month:"long",day:"numeric",year:"numeric"},e.moreLinkContent=void 0,e.weekNumbers=!1,e.weekNumberContent=void 0,e.buttonText.dayGridMonth="month",e.buttonText.close="Close",e.theme.uniform="ec-uniform",e.theme.dayFoot="ec-day-foot",e.theme.popup="ec-popup",e.theme.weekNumber="ec-week-number",e.view="dayGridMonth",e.views.dayGridMonth={buttonText:Xi,component:Gu,dayHeaderFormat:{weekday:"short"},dayHeaderAriaLabelFormat:{weekday:"long"},displayEventEnd:!1,duration:{months:1},theme:At("ec-day-grid ec-month-view"),titleFormat:{year:"numeric",month:"long"}}},createStores(e){e._days=Du(e),e._intlDayCell=Rn(e.locale,e.dayCellFormat),e._intlDayPopover=Rn(e.locale,e.dayPopoverFormat),e._hiddenEvents=Ye({}),e._popupDate=Ye(null),e._popupChunks=Ye([])}};function Mo(e,t,n){return e.startEditable??t??e.editable??n}function qu(e,t,n){return e.durationEditable??t??e.editable??n}let Qi=!1;function Vu(e){Qi||(Qi=!0,window.requestAnimationFrame(()=>{e(),Qi=!1}))}function Ho(e,t,n){return gt(t,yn(n,e))}function Xu(e,t){fe(t,!1);const[n,r]=$e(),i=()=>u(Te,"$eventStartEditable",n),o=()=>u(Me,"$editable",n),l=()=>u(ia,"$slotDuration",n),d=()=>u(Ke,"$selectable",n),s=()=>u(qe,"$view",n),f=()=>u(ue,"$_bodyEl",n),v=()=>u(ee,"$datesAboveResources",n),h=()=>u(br,"$selectLongPressDelay",n),c=()=>u(re,"$eventLongPressDelay",n),m=()=>u(ln,"$longPressDelay",n),_=()=>u(ra,"$selectMinDistance",n),y=()=>u(te,"$eventDragMinDistance",n),g=()=>u(M,"$_iEvents",n),$=()=>u(pe,"$eventResizeStart",n),R=()=>u(he,"$eventDragStart",n),T=()=>u(Mt,"$resizeConstraint",n),b=()=>u(sn,"$selectConstraint",n),w=()=>u(ge,"$dragConstraint",n),p=()=>u(Ae,"$dragScroll",n),D=()=>u(ne,"$slotHeight",n),C=()=>u(Ee,"$slotWidth",n),x=()=>u(et,"$unselectAuto",n),H=()=>u(Wt,"$unselectCancel",n),I=()=>u(Ht,"$selectFn",n),N=()=>u(Ge,"$eventResizeStop",n),z=()=>u(Re,"$eventDragStop",n),S=()=>u(ke,"$_view",n),A=()=>u($t,"$eventResize",n),B=()=>u(G,"$eventDrop",n),F=()=>u(De,"$dateClick",n),E=()=>u(Tt,"$validRange",n),O=()=>u(me,"$_dayGrid",n),U=()=>u(_e,"$_events",n),Q=()=>u(Pt,"$selectBackgroundColor",n),le=()=>u(at,"$unselectFn",n);let{_iEvents:M,_iClass:ae,_events:_e,_view:ke,_dayGrid:me,_bodyEl:ue,datesAboveResources:ee,dateClick:De,dragConstraint:ge,dragScroll:Ae,editable:Me,eventStartEditable:Te,eventDragMinDistance:te,eventDragStart:he,eventDragStop:Re,eventDrop:G,eventLongPressDelay:re,eventResizeStart:pe,eventResizeStop:Ge,eventResize:$t,longPressDelay:ln,resizeConstraint:Mt,selectable:Ke,select:Ht,selectBackgroundColor:Pt,selectConstraint:sn,selectLongPressDelay:br,selectMinDistance:ra,slotDuration:ia,slotHeight:ne,slotWidth:Ee,unselect:at,unselectAuto:et,unselectCancel:Wt,validRange:Tt,view:qe}=be("state");const It=1,jn=2,zo=3,Bo=4,aa=5,di=6;let ot,er,ce,oa,zt,la,Nt,tr,sa,ua,ht,nt,Zt,Ft,wr,ci,Jt,un,fi,Je,vi,hi,da,Dr=vn(),Er,kr,nr;function Go(Y){return Mo(Y,i(),o())}function Yo(Y,V,Oe,ye){ot||(ot=ha(V)?It:di,An()&&(ce=Y,ca(V),Oe&&(zt=Oe),ye&&(kr=ye),fi="dragging",rr(V)))}function qo(Y,V,Oe,ye,Bt,xr,ma){ot||(ot=ha(V)?Oe?zo:jn:di,An()&&(ce=Y,ca(V),Bt&&(zt=Bt),xr&&(kr=xr),fi=ye==="x"?"resizingX":"resizingY",_i()?(Je=J(ce.end),un?(Ui(Je,ce.start),Je>=ce.end&&Cn(Je)):(Zr(Je,l()),Je<ce.start&&(Je=ce.start),zt=ce.start)):(Je=J(ce.start),un?(Ui(Je,ce.end),Je<=ce.start&&!ma&&ft(Je)):(xe(Je,l()),Je>ce.end&&(Je=ce.end),zt=ce.end,ma||(zt=Zr(J(zt),l()))),ma&&!un&&(nr=l())),rr(V)))}function Vo(Y){ot||(ot=ha(Y)?d()&&!go(s())?Bo:aa:di,An()&&(ca(Y),fi="selecting",vi=un?tt({day:1}):l(),ce={allDay:un,start:zt,end:xe(J(zt),vi),resourceIds:Nt?[Nt.id]:[]},rr(Y)))}function Xo(){ot||(ot=di)}function ca(Y){window.getSelection().removeAllRanges(),sa=ht=Y.clientX,ua=nt=Y.clientY;let V=qn(ht,nt);({allDay:un,date:zt,resource:Nt}=an(V)(ht,nt)),Yi(s())?Zt=wr=f():(Zt=Sn(V,Nt?4:3),wr=Sn(V,Nt&&(Sc()||v())?2:1)),Zo(),Y.pointerType!=="mouse"&&j(Dr,setTimeout(()=>{ot&&(er=!0,rr(Y))},(Ln()?h():c())??m()))}function rr(Y){if(er||Y&&Y.pointerType==="mouse"&&xc()>=(Ln()?_():y())){er=!0,Cr(Y),de(ae,fi),g()[0]||(Ln()?Cc():pc(Y,pr()?$():R()));let V=va(Tc());if(V){let Oe;if({allDay:Oe,date:la,resource:tr}=V,Oe===un){let ye=Tr({},g()[0]),Bt=T();Jt=tt((la-zt)/1e3),_i()?(ye.start=xe(J(ce.start),Jt),ye.start>Je&&(ye.start=Je,Jt=tt((Je-ce.start)/1e3))):(ye.end=xe(J(ce.end),Jt),nr&&xe(ye.end,nr),pr()?ye.end<Je&&(ye.end=Je,Jt=tt((Je-ce.end)/1e3)):Ln()?(ye.end<ce.end?(ye.start=Zr(ye.end,vi),ye.end=ce.end):ye.start=ce.start,Bt=b()):(ye.start=xe(J(ce.start),Jt),Nt&&(ye.resourceIds=ce.resourceIds.filter(xr=>xr!==Nt.id),ye.resourceIds.push(tr.id)),Bt=w()));do{if(Bt!==void 0&&(ye=Tr(Vi(ce),ye),Bt(Ln()?jo(ye,Y):el(ye,ce,Y))===!1)){Qe(M,Z(g)[0]=Tr(g()[0],ce),Z(g));break}Qe(M,Z(g)[0]=Tr(g()[0],ye),Z(g))}while(!1)}}}if(p()){let V=D()*2,Oe=C();Vu(()=>{Zt&&(nt<V&&window.scrollBy(0,gt(-10,(nt-V)/3)),nt<Ft.top+V&&(Zt.scrollTop+=gt(-10,(nt-Ft.top-V)/3)),nt>window.innerHeight-V&&window.scrollBy(0,yn(10,(nt-window.innerHeight+V)/3)),nt>Ft.bottom-V&&(Zt.scrollTop+=yn(10,(nt-Ft.bottom+V)/3)),Yi(s())&&(ht<Ft.left+Oe&&(Zt.scrollLeft+=gt(-10,(ht-Ft.left-Oe)/3)),ht>Ft.right-Oe&&(Zt.scrollLeft+=yn(10,(ht-Ft.right+Oe)/3))))})}}function fa(){An()&&(Zo(),rr())}function kc(Y){An()&&Y.isPrimary&&(ht=Y.clientX,nt=Y.clientY,rr(Y))}function Ko(Y){if(hi&&x()&&!(H()&&Y.target.closest(H()))&&Cr(Y),ot&&Y.isPrimary){if(er)if(Ln()){if(hi=!0,Se(I())){let V=jo(g()[0],Y);I()(V)}}else{ce.display=oa;let V=pr()?N():z();Se(V)&&V({event:kt(ce),jsEvent:Y,view:yt(S())});let Oe=Vi(ce);if(Qo(ce,g()[0]),Jo(),V=pr()?A():B(),Se(V)){let ye=ce,Bt=el(ce,Oe,Y);V(xn(Bt,{revert(){Qo(ye,Oe)}}))}}else if((Rc()||Ln())&&Se(F())&&!da){ht=Y.clientX,nt=Y.clientY;let V=qn(ht,nt);if(V){let{allDay:Oe,date:ye,resource:Bt}=an(V)(ht,nt);F()({allDay:Oe,date:je(ye),dateStr:lt(ye),dayEl:V,jsEvent:Y,view:yt(S()),resource:Bt})}}er=!1,ot=sa=ua=ht=nt=ce=oa=zt=la=Nt=tr=Jt=nr=un=de(ae,Je=vi=kr=void 0),Zt=wr=Ft=ci=void 0,a(Dr)&&(clearTimeout(a(Dr)),j(Dr,void 0))}da=!1}function Tc(){return qn(Ho(ht,Er[0],Er[1]),Ho(nt,Er[2],Er[3]))}function va(Y){if(Y){let V=an(Y)(ht,nt);if(V.disabled){if(!E().end||V.date<E().end)return va(Y.nextElementSibling);if(!E().start||V.date>E().start)return va(Y.previousElementSibling)}else return V}return null}function Zo(){Ft=Lt(Zt),ci=Lt(wr),Er=[gt(0,ci.left+(Yi(s())?1:O()?0:8)),yn(document.documentElement.clientWidth,ci.left+wr.clientWidth)-2,gt(0,Ft.top),yn(document.documentElement.clientHeight,Ft.top+Zt.clientHeight)-2]}function pc(Y,V){Se(V)&&V({event:kt(ce),jsEvent:Y,view:yt(S())}),oa=ce.display,ce.display="preview",Qe(M,Z(g)[0]=Vi(ce),Z(g)),kr!==void 0&&Qe(M,Z(g)[0]._margin=kr,Z(g)),nr&&xe(g()[0].end,nr),ce.display="ghost",de(_e,U())}function Cc(){Qe(M,Z(g)[0]={id:"{select}",allDay:ce.allDay,start:ce.start,title:"",display:"preview",extendedProps:{},backgroundColor:Q(),resourceIds:ce.resourceIds,classNames:[],styles:[]},Z(g))}function Jo(){Qe(M,Z(g)[0]=null,Z(g))}function Tr(Y,V){return Y.start=V.start,Y.end=V.end,Y.resourceIds=V.resourceIds,Y}function Qo(Y,V){Tr(Y,V),de(_e,U())}function jo(Y,V){let{start:Oe,end:ye}=kt(Y);return{start:Oe,end:ye,startStr:lt(Y.start),endStr:lt(Y.end),allDay:un,view:yt(S()),resource:Nt,jsEvent:V}}function el(Y,V,Oe){let ye;return pr()?ye=_i()?{startDelta:Jt,endDelta:tt(0)}:{startDelta:tt(0),endDelta:Jt}:ye={delta:Jt,oldResource:Nt!==tr?Nt:void 0,newResource:Nt!==tr?tr:void 0},xn(ye,{event:kt(Y),oldEvent:kt(V),view:yt(S()),jsEvent:Oe}),ye}function xc(){return Math.sqrt(Math.pow(ht-sa,2)+Math.pow(nt-ua,2))}function Sc(){return ot===It}function pr(){return ot===jn||_i()}function _i(){return ot===zo}function Rc(){return ot===aa}function Ln(){return ot===Bo}function An(){return ot&&ot<aa}function ha(Y){return Y.isPrimary&&(Y.pointerType!=="mouse"||Y.buttons&1)}function Cr(Y){hi&&(hi=!1,Jo(),Se(le())&&le()({jsEvent:Y,view:yt(S())}))}function tl(){da=!0}ke.subscribe(Cr);function Lc(Y){if(An()){let V=Y.target,Oe=[],ye=()=>ho(Oe);Oe.push(gr(window,"touchmove",Rs,{passive:!1})),Oe.push(gr(V,"touchmove",_a(()=>er))),Oe.push(gr(V,"touchend",ye)),Oe.push(gr(V,"touchcancel",ye))}}function _a(Y){return V=>{Y()&&V.preventDefault()}}pn(),Ve("pointermove",mt,kc),Ve("pointerup",mt,Ko),Ve("pointercancel",mt,Ko),Ve("scroll",mt,fa);var Ac=k(()=>_a(An));Ve("selectstart",mt,function(...Y){var V;(V=a(Ac))==null||V.apply(this,Y)});var Mc=k(()=>_a(()=>a(Dr)));Ve("contextmenu",mt,function(...Y){var V;(V=a(Mc))==null||V.apply(this,Y)}),Ve("touchstart",mt,Lc,void 0,!0),gn(t,"draggable",Go),gn(t,"drag",Yo),gn(t,"resize",qo),gn(t,"select",Vo),gn(t,"noAction",Xo),gn(t,"handleScroll",fa),gn(t,"unselect",Cr),gn(t,"noClick",tl);var Hc=ve({draggable:Go,drag:Yo,resize:qo,select:Vo,noAction:Xo,handleScroll:fa,unselect:Cr,noClick:tl});return r(),Hc}function Ku(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(l,"$_iEvents",n),o=()=>u(d,"$slotDuration",n);let{_iEvents:l,slotDuration:d}=be("state"),s=0,f=0;Ze(()=>{i()[0]&&_()});function v(){let $=qn(s,f);if($){let{allDay:R,date:T,resource:b,disabled:w}=an($)(s,f);if(!w){let p=R?2:1;i()[p]||m(p),Qe(l,Z(i)[p].start=T,Z(i)),Qe(l,Z(i)[p].end=xe(J(T),o()),Z(i)),Qe(l,Z(i)[p].resourceIds=b?[b.id]:[],Z(i));return}}_()}function h(){v()}function c($){y($)&&(s=$.clientX,f=$.clientY,v())}function m($){Qe(l,Z(i)[$]={id:"{pointer}",title:"",display:"pointer",extendedProps:{},backgroundColor:"transparent",classNames:[],styles:[]},Z(i))}function _(){i()[1]&&Qe(l,Z(i)[1]=null,Z(i)),i()[2]&&Qe(l,Z(i)[2]=null,Z(i))}function y($){return $.isPrimary&&$.pointerType==="mouse"}Ve("pointermove",mt,c),Ve("scroll",mt,h);var g=ve({handleScroll:h});return r(),g}var Zu=W("<div></div>"),Ju=W("<div></div>"),Qu=W("<!> <!> <!>",1);function ju(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(c,"$eventDurationEditable",n),o=()=>u(_,"$editable",n),l=()=>u(y,"$_interaction",n),d=()=>u(m,"$eventResizableFromStart",n),s=()=>u(h,"$theme",n);let f=Be(t,"forceDate",3,void 0),v=Be(t,"forceMargin",3,void 0),{theme:h,eventDurationEditable:c,eventResizableFromStart:m,editable:_,_interaction:y}=be("state"),g=k(()=>t.chunk.event),$=k(()=>t.chunk.event.display),R=k(()=>!vt(a($))&&!Xn(a($))&&qu(a(g),i(),o()));function T(H){return I=>{var N,z;return l().action.resize(a(g),I,H,t.axis,(N=f())==null?void 0:N(),(z=v())==null?void 0:z(),t.chunk.zeroDuration)}}var b=Qu(),w=se(b);{var p=H=>{var I=Zu(),N=k(()=>T(!0));I.__pointerdown=function(...z){var S;(S=a(N))==null||S.apply(this,z)},q(()=>P(I,1,`${s().resizer??""} ${s().start??""}`)),L(H,I)};oe(w,H=>{a(R)&&d()&&H(p)})}var D=ie(w,2);Tn(D,()=>t.children);var C=ie(D,2);{var x=H=>{var I=Ju(),N=k(()=>T(!1));I.__pointerdown=function(...z){var S;(S=a(N))==null||S.apply(this,z)},q(()=>P(I,1,s().resizer)),L(H,I)};oe(C,H=>{a(R)&&H(x)})}L(e,b),ve(),r()}qt(["pointerdown"]);var ed=W("<!> <!>",1);function td(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(y,"$_interaction",n),o=()=>u(v,"$theme",n),l=()=>u(c,"$eventStartEditable",n),d=()=>u(h,"$editable",n),s=()=>u(_,"$_bodyEl",n),f=()=>u(m,"$pointer",n);let{theme:v,editable:h,eventStartEditable:c,pointer:m,_bodyEl:_,_interaction:y,_iClasses:g}=be("state");Qe(y,Z(i).resizer=ju,Z(i)),Ze(()=>{o(),l(),d(),de(g,(p,D)=>{let{display:C}=D;return[...p,Xn(C)?[o()[C]]:!vt(C)&&Mo(D,l(),d())?[o().draggable]:[]]})}),Ze(()=>{if(s())return gr(s(),"scroll",$)});function $(){var p;for(let D of Object.values(i()))(p=D==null?void 0:D.handleScroll)==null||p.call(D)}var R=ed(),T=se(R);ze(Xu(T,{}),p=>Qe(y,Z(i).action=p,Z(i)),()=>{var p;return(p=i())==null?void 0:p.action});var b=ie(T,2);{var w=p=>{ze(Ku(p,{}),D=>Qe(y,Z(i).pointer=D,Z(i)),()=>{var D;return(D=i())==null?void 0:D.pointer})};oe(b,p=>{f()&&p(w)})}L(e,R),ve(),r()}const nd={createOptions(e){e.dateClick=void 0,e.dragConstraint=void 0,e.dragScroll=!0,e.editable=!1,e.eventDragMinDistance=5,e.eventDragStart=void 0,e.eventDragStop=void 0,e.eventDrop=void 0,e.eventDurationEditable=!0,e.eventLongPressDelay=void 0,e.eventResizableFromStart=!1,e.eventResizeStart=void 0,e.eventResizeStop=void 0,e.eventResize=void 0,e.eventStartEditable=!0,e.longPressDelay=1e3,e.pointer=!1,e.resizeConstraint=void 0,e.select=void 0,e.selectBackgroundColor=void 0,e.selectConstraint=void 0,e.selectLongPressDelay=void 0,e.selectMinDistance=5,e.unselect=void 0,e.unselectAuto=!0,e.unselectCancel="",e.theme.draggable="ec-draggable",e.theme.ghost="ec-ghost",e.theme.preview="ec-preview",e.theme.pointer="ec-pointer",e.theme.resizer="ec-resizer",e.theme.start="ec-start",e.theme.dragging="ec-dragging",e.theme.resizingY="ec-resizing-y",e.theme.resizingX="ec-resizing-x",e.theme.selecting="ec-selecting"},createStores(e){e._auxiliary.update(t=>[...t,td])}};var rd=W("<div></div> <!>",1);function id(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(d,"$_interaction",n),o=()=>u(l,"$theme",n);let{theme:l,_interaction:d}=be("state"),s=k(()=>v=>(delete v["background-color"],delete v.color,v));const f=k(()=>{var v;return(v=i().action)==null?void 0:v.noAction});Ao(e,{get chunk(){return t.chunk},get styles(){return a(s)},get onpointerdown(){return a(f)},body:(h,c=bt,m=bt,_=bt)=>{var y=rd(),g=se(y);let $;var R=ie(g,2);Tn(R,c),q(()=>{P(g,1,o().eventTag),$=Vt(g,"",$,{"background-color":m()})}),L(h,y)},$$slots:{body:!0}}),ve(),r()}var ad=W('<div role="listitem"><h4><time></time> <time></time></h4> <!></div>');function od(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(R,"$_today",n),o=()=>u(w,"$highlightedDates",n),l=()=>u(D,"$validRange",n),d=()=>u(_,"$_filteredEvents",n),s=()=>u(b,"$filterEventsWithResources",n),f=()=>u(T,"$resources",n),v=()=>u(p,"$theme",n),h=()=>u(y,"$_interaction",n),c=()=>u(g,"$_intlListDay",n),m=()=>u($,"$_intlListDaySide",n);let{_filteredEvents:_,_interaction:y,_intlListDay:g,_intlListDaySide:$,_today:R,resources:T,filterEventsWithResources:b,highlightedDates:w,theme:p,validRange:D}=be("state"),C=He(void 0),x=k(()=>Pe(t.date,i())),H=k(()=>o().some(F=>Pe(F,t.date))),I=k(()=>Kn(t.date,l())),N=k(()=>lt(t.date,10)),z=k(()=>{let F=[];if(!a(I)){let E=t.date,O=ft(J(t.date));for(let U of d())if(!vt(U.display)&&on(U,E,O,s()?f():void 0)){let Q=Kt(U,E,O);F.push(Q)}ei(F)}return F});Ze(()=>{a(C)&&Gn(a(C),()=>({allDay:!0,date:t.date,resource:void 0,dayEl:a(C),disabled:a(I)}))});var S=Ce(),A=se(S);{var B=F=>{var E=ad();E.__pointerdown=function(...M){var ae,_e;(_e=(ae=h().action)==null?void 0:ae.select)==null||_e.apply(this,M)};var O=X(E),U=X(O);Ue(U,(M,ae)=>K==null?void 0:K(M,ae),()=>c().format(t.date));var Q=ie(U,2);Ue(Q,(M,ae)=>K==null?void 0:K(M,ae),()=>m().format(t.date));var le=ie(O,2);we(le,17,()=>a(z),M=>M.event,(M,ae)=>{id(M,{get chunk(){return a(ae)}})}),ze(E,M=>j(C,M),()=>a(C)),q(M=>{P(E,1,`${v().day??""} ${M??""}${a(x)?" "+v().today:""}${a(H)?" "+v().highlight:""}`),P(O,1,v().dayHead),We(U,"datetime",a(N)),P(Q,1,v().daySide),We(Q,"datetime",a(N))},[()=>{var M;return(M=v().weekdays)==null?void 0:M[t.date.getUTCDay()]}]),L(F,E)};oe(A,F=>{a(z).length&&F(B)})}L(e,S),ve(),r()}qt(["pointerdown"]);function ld(e,t,n,r,i){Se(t())&&t()({jsEvent:e,view:yt(r())})}var sd=W("<div></div>"),ud=W("<div><div><!></div></div>");function dd(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(_,"$_viewDates",n),o=()=>u(c,"$_filteredEvents",n),l=()=>u(g,"$noEventsContent",n),d=()=>u(y,"$noEventsClick",n),s=()=>u(m,"$_view",n),f=()=>u(h,"$_bodyEl",n),v=()=>u($,"$theme",n);let{_bodyEl:h,_filteredEvents:c,_view:m,_viewDates:_,noEventsClick:y,noEventsContent:g,theme:$}=be("state"),R=k(()=>{let x=!0;if(i().length){let H=i()[0],I=ft(J(i().at(-1)));for(let N of o())if(!vt(N.display)&&N.start<I&&N.end>H){x=!1;break}}return x}),T=k(()=>Se(l())?l()():l());var b=ud(),w=X(b),p=X(w);{var D=x=>{var H=sd();H.__click=[ld,d,y,s,m],Ue(H,(I,N)=>K==null?void 0:K(I,N),()=>a(T)),q(()=>P(H,1,v().noEvents)),L(x,H)},C=x=>{var H=Ce(),I=se(H);we(I,1,i,Ie,(N,z)=>{od(N,{get date(){return a(z)}})}),L(x,H)};oe(p,x=>{a(R)?x(D):x(C,!1)})}ze(b,x=>de(h,x),()=>f()),q(()=>{P(b,1,v().body),P(w,1,v().content)}),L(e,b),ve(),r()}qt(["click"]);function ai(e){dd(e,{})}const cd={createOptions(e){e.buttonText.listDay="list",e.buttonText.listWeek="list",e.buttonText.listMonth="list",e.buttonText.listYear="list",e.listDayFormat={weekday:"long"},e.listDaySideFormat={year:"numeric",month:"long",day:"numeric"},e.noEventsClick=void 0,e.noEventsContent="No events",e.theme.daySide="ec-day-side",e.theme.eventTag="ec-event-tag",e.theme.noEvents="ec-no-events",e.view="listWeek",e.views.listDay={buttonText:ti,component:ai,duration:{days:1},theme:At("ec-list ec-day-view")},e.views.listWeek={buttonText:ni,component:ai,duration:{weeks:1},theme:At("ec-list ec-week-view")},e.views.listMonth={buttonText:Xi,component:ai,duration:{months:1},theme:At("ec-list ec-month-view")},e.views.listYear={buttonText:zs,component:ai,duration:{years:1},theme:At("ec-list ec-year-view")}},createStores(e){e._intlListDay=Rn(e.locale,e.listDayFormat),e._intlListDaySide=Rn(e.locale,e.listDaySideFormat)}};function fd(e){return it([e.slotDuration,e.slotLabelInterval,e._slotTimeLimits,e._intlSlotLabel],t=>Co(Et(Xt()),...t))}function vd(e){return it([e.slotMinTime,e.slotMaxTime,e.flexibleSlotTimeLimits,e._viewDates,e._filteredEvents],t=>xo(...t))}function hd(e){if(!e.length)return;ei(e);let t={columns:[],end:e[0].end};for(let n of e){let r=0;if(n.start<t.end){for(;r<t.columns.length&&!(t.columns[r].at(-1).end<=n.start);++r);n.end>t.end&&(t.end=n.end)}else t={columns:[],end:n.end};t.columns.length<r+1&&t.columns.push([]),t.columns[r].push(n),n.group=t,n.column=r}}function _d(e){let t="all-day",n;return e?(n=Se(e)?e({text:t}):e,typeof n=="string"&&(n={html:n})):n={html:t},n}var md=W("<time></time>"),gd=W('<div><div></div> <!></div> <div role="row"><div><!></div> <!></div>',1);function $r(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(s,"$allDayContent",n),o=()=>u(f,"$slotLabelInterval",n),l=()=>u(v,"$theme",n),d=()=>u(h,"$_times",n);let{allDayContent:s,slotLabelInterval:f,theme:v,_times:h}=be("state"),c=k(()=>_d(i())),m=k(()=>o()&&o().seconds<=0);var _=gd(),y=se(_),g=X(y);Ue(g,(C,x)=>K==null?void 0:K(C,x),()=>a(c));var $=ie(g,2);we($,1,d,Ie,(C,x,H)=>{var I=md();Ue(I,(N,z)=>K==null?void 0:K(N,z),()=>a(x)[1]),q(()=>{P(I,1,`${l().time??""}${(H||a(m))&&a(x)[2]?"":" "+l().minor}`),We(I,"datetime",a(x)[0])}),L(C,I)});var R=ie(y,2),T=X(R),b=X(T);{var w=C=>{var x=Ce(),H=se(x);Tn(H,()=>t.lines),L(C,x)};oe(b,C=>{t.lines&&C(w)})}var p=ie(T,2);{var D=C=>{var x=Ce(),H=se(x);Tn(H,()=>t.children),L(C,x)};oe(p,C=>{t.children&&C(D)})}q(()=>{P(y,1,l().sidebar),P(g,1,l().sidebarTitle),P(R,1,l().days),P(T,1,l().lines)}),L(e,_),ve(),r()}var yd=W("<div></div>"),$d=W("<div><div><!></div></div>");function Po(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(c,"$_viewDates",n),o=()=>u(g,"$scrollTime",n),l=()=>u(m,"$_slotTimeLimits",n),d=()=>u($,"$slotDuration",n),s=()=>u(R,"$slotHeight",n),f=()=>u(T,"$theme",n),v=()=>u(_,"$_times",n);let{_bodyEl:h,_viewDates:c,_slotTimeLimits:m,_times:_,_recheckScrollable:y,scrollTime:g,slotDuration:$,slotHeight:R,theme:T}=be("state"),b=He(void 0);Ze(()=>{de(h,a(b))}),Ze(()=>{i(),o(),Z(w)});function w(){a(b).scrollTop=((o().seconds-l().min.seconds)/d().seconds-.5)*s()}var p=$d(),D=X(p),C=X(D);$r(C,{get children(){return t.children},lines:H=>{var I=Ce(),N=se(I);we(N,1,v,Ie,(z,S)=>{var A=yd();q(()=>P(A,1,`${f().line??""}${a(S)[2]?"":" "+f().minor}`)),L(z,A)}),L(H,I)},$$slots:{lines:!0}}),ze(p,x=>j(b,x),()=>a(b)),Ue(p,(x,H)=>Rt==null?void 0:Rt(x,H),()=>()=>de(y,!0)),q(()=>{P(p,1,f().body),P(D,1,f().content)}),L(e,p),ve(),r()}function oi(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(f,"$slotDuration",n),o=()=>u(h,"$_slotTimeLimits",n),l=()=>u(v,"$slotHeight",n),d=()=>u(s,"$slotEventOverlap",n);let{slotEventOverlap:s,slotDuration:f,slotHeight:v,_slotTimeLimits:h}=be("state"),c=k(()=>t.chunk.event.display),m=k(()=>_=>{let y=i().seconds,g=o().min.seconds,$=(t.chunk.start-t.date)/1e3,R=(t.chunk.end-t.date)/1e3,T=($-g)/y*l(),b=(R-$)/y*l()||l(),w=(o().max.seconds-$)/y*l();return _.top=`${T}px`,_["min-height"]=`${b}px`,_.height=`${b}px`,_["max-height"]=`${w}px`,(!vt(a(c))&&!Xn(a(c))||wo(a(c)))&&(_["z-index"]=`${t.chunk.column+1}`,_.left=`${100/t.chunk.group.columns.length*t.chunk.column}%`,_.width=`${100/t.chunk.group.columns.length*(d()?.5*(1+t.chunk.group.columns.length-t.chunk.column):1)}%`),_});ii(e,{get chunk(){return t.chunk},get styles(){return a(m)},axis:"y"}),ve(),r()}var bd=W("<div></div>");function wd(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(m,"$_now",n),o=()=>u(_,"$_today",n),l=()=>u(v,"$slotDuration",n),d=()=>u(y,"$_slotTimeLimits",n),s=()=>u(h,"$slotHeight",n),f=()=>u(c,"$theme",n);let{slotDuration:v,slotHeight:h,theme:c,_now:m,_today:_,_slotTimeLimits:y}=be("state"),g=k(()=>(i()-o())/1e3),$=k(()=>{let T=l().seconds,b=d().min.seconds;return(a(g)-b)/T*s()});var R=bd();q(()=>{P(R,1,f().nowIndicator),Vt(R,`top:${a($)??""}px`)}),L(e,R),ve(),r()}var Dd=W("<!> <!> <!>",1),Ed=W('<div role="cell"><div><!></div> <div><!></div> <div><!></div></div>');function Io(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(z,"$_today",n),o=()=>u(b,"$highlightedDates",n),l=()=>u(I,"$validRange",n),d=()=>u(S,"$_slotTimeLimits",n),s=()=>u(C,"$filterEventsWithResources",n),f=()=>u(H,"$resources",n),v=()=>u(R,"$_filteredEvents",n),h=()=>u(T,"$_iEvents",n),c=()=>u(p,"$slotDuration",n),m=()=>u(D,"$slotHeight",n),_=()=>u(x,"$theme",n),y=()=>u(N,"$_interaction",n),g=()=>u(w,"$nowIndicator",n);let $=Be(t,"resource",3,void 0),{_filteredEvents:R,_iEvents:T,highlightedDates:b,nowIndicator:w,slotDuration:p,slotHeight:D,filterEventsWithResources:C,theme:x,resources:H,validRange:I,_interaction:N,_today:z,_slotTimeLimits:S}=be("state"),A=He(void 0),B=k(()=>Pe(t.date,i())),F=k(()=>o().some(G=>Pe(G,t.date))),E=k(()=>Kn(t.date,l())),O=k(()=>xe(J(t.date),d().min)),U=k(()=>xe(J(t.date),d().max)),Q=k(()=>$()??(s()?f():void 0)),le=k(()=>{if(a(E))return[[],[]];let G=[],re=[];for(let pe of v())if((!pe.allDay||vt(pe.display))&&on(pe,a(O),a(U),a(Q))){let Ge=Kt(pe,a(O),a(U));switch(pe.display){case"background":re.push(Ge);break;default:G.push(Ge)}}return hd(G),[G,re]}),M=k(()=>Mn(a(le),2)),ae=k(()=>a(M)[0]),_e=k(()=>a(M)[1]),ke=k(()=>a(E)?[]:h().map(G=>G&&on(G,a(O),a(U),$())?Kt(G,a(O),a(U)):null));function me(G,re){return re-=Lt(a(A)).top,{allDay:!1,date:xe(xe(J(t.date),d().min),c(),fo(re/m())),resource:$(),dayEl:a(A),disabled:a(E)}}Bn(()=>{Gn(a(A),me)});var ue=Ed();ue.__pointerdown=function(...G){var re,pe;(pe=a(E)||(re=y().action)==null?void 0:re.select)==null||pe.apply(this,G)};var ee=X(ue),De=X(ee);{var ge=G=>{var re=Ce(),pe=se(re);we(pe,17,()=>a(_e),Ge=>Ge.event,(Ge,$t)=>{oi(Ge,{get date(){return t.date},get chunk(){return a($t)}})}),L(G,re)};oe(De,G=>{a(E)||G(ge)})}var Ae=ie(ee,2),Me=X(Ae);{var Te=G=>{var re=Dd(),pe=se(re);{var Ge=Ke=>{oi(Ke,{get date(){return t.date},get chunk(){return a(ke)[1]}})};oe(pe,Ke=>{a(ke)[1]&&Ke(Ge)})}var $t=ie(pe,2);we($t,17,()=>a(ae),Ke=>Ke.event,(Ke,Ht)=>{oi(Ke,{get date(){return t.date},get chunk(){return a(Ht)}})});var ln=ie($t,2);{var Mt=Ke=>{oi(Ke,{get date(){return t.date},get chunk(){return a(ke)[0]}})};oe(ln,Ke=>{a(ke)[0]&&!a(ke)[0].event.allDay&&Ke(Mt)})}L(G,re)};oe(Me,G=>{a(E)||G(Te)})}var te=ie(Ae,2),he=X(te);{var Re=G=>{wd(G,{})};oe(he,G=>{g()&&a(B)&&!a(E)&&G(Re)})}ze(ue,G=>j(A,G),()=>a(A)),q(G=>{P(ue,1,`${_().day??""} ${G??""}${a(B)?" "+_().today:""}${a(F)?" "+_().highlight:""}${a(E)?" "+_().disabled:""}`),P(ee,1,_().bgEvents),P(Ae,1,_().events),P(te,1,_().extra)},[()=>{var G;return(G=_().weekdays)==null?void 0:G[t.date.getUTCDay()]}]),L(e,ue),ve(),r()}qt(["pointerdown"]);function ji(e,t){fe(t,!0);let n=Be(t,"longChunks",19,()=>({})),r=He(void 0),i=He(1),o=k(()=>t.chunk.event),l=k(()=>t.chunk.event.display),d=k(()=>f=>(vt(a(l))?f.width=`calc(${t.chunk.days*100}% + ${t.chunk.days-1}px)`:(f.width=`calc(${t.chunk.days*100}% + ${(t.chunk.days-1)*7}px)`,f["margin-top"]=`${a(o)._margin??a(i)}px`),f));function s(){a(r)&&j(i,bo(t.chunk,n(),Yn(a(r))),!0)}return ii(e,{get chunk(){return t.chunk},get styles(){return a(d)},axis:"x",forceMargin:()=>Lt(a(r)).top-Lt(Sn(a(r),1)).top,get el(){return a(r)},set el(f){j(r,f,!0)}}),ve({reposition:s})}var kd=W("<div><!></div>"),Td=W('<div role="cell"><div><!></div> <!> <div><!></div></div>');function pd(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(y,"$_today",n),o=()=>u(h,"$highlightedDates",n),l=()=>u(m,"$validRange",n),d=()=>u(c,"$theme",n),s=()=>u(_,"$_interaction",n);let f=Be(t,"iChunks",19,()=>[]),v=Be(t,"resource",3,void 0),{highlightedDates:h,theme:c,validRange:m,_interaction:_,_today:y}=be("state"),g=He(void 0),$=[],R=k(()=>t.chunks.filter(E=>Pe(E.date,t.date))),T=k(()=>t.bgChunks.filter(E=>Pe(E.date,t.date))),b=k(()=>Pe(t.date,i())),w=k(()=>o().some(E=>Pe(E,t.date))),p=k(()=>Kn(t.date,l()));Bn(()=>{Gn(a(g),()=>({allDay:!0,date:t.date,resource:v(),dayEl:a(g),disabled:a(p)}))});function D(){a(p)||$n($,a(R))}var C=Td();C.__pointerdown=function(...E){var O,U;(U=a(p)||(O=s().action)==null?void 0:O.select)==null||U.apply(this,E)};var x=X(C),H=X(x);{var I=E=>{var O=Ce(),U=se(O);we(U,17,()=>a(T),Q=>Q.event,(Q,le)=>{ji(Q,{get chunk(){return a(le)}})}),L(E,O)};oe(H,E=>{a(p)||E(I)})}var N=ie(x,2);{var z=E=>{var O=kd(),U=X(O);ji(U,{get chunk(){return f()[0]}}),q(()=>P(O,1,`${d().events??""} ${d().preview??""}`)),L(E,O)};oe(N,E=>{f()[0]&&Pe(f()[0].date,t.date)&&!a(p)&&E(z)})}var S=ie(N,2),A=X(S);{var B=E=>{var O=Ce(),U=se(O);we(U,19,()=>a(R),Q=>Q.event,(Q,le,M)=>{ze(ji(Q,{get chunk(){return a(le)},get longChunks(){return t.longChunks}}),(ae,_e)=>$[_e]=ae,ae=>$==null?void 0:$[ae],()=>[a(M)])}),L(E,O)};oe(A,E=>{a(p)||E(B)})}ze(C,E=>j(g,E),()=>a(g)),q(E=>{P(C,1,`${d().day??""} ${E??""}${a(b)?" "+d().today:""}${a(w)?" "+d().highlight:""}${a(p)?" "+d().disabled:""}`),P(x,1,d().bgEvents),P(S,1,d().events)},[()=>{var E;return(E=d().weekdays)==null?void 0:E[t.date.getUTCDay()]}]),L(e,C);var F=ve({reposition:D});return r(),F}qt(["pointerdown"]);function ea(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(g,"$validRange",n),o=()=>u(y,"$filterEventsWithResources",n),l=()=>u(_,"$resources",n),d=()=>u(h,"$_filteredEvents",n),s=()=>u(m,"$hiddenDays",n),f=()=>u(c,"$_iEvents",n);let v=Be(t,"resource",3,void 0),{_filteredEvents:h,_iEvents:c,hiddenDays:m,resources:_,filterEventsWithResources:y,validRange:g}=be("state"),$=[],R=k(()=>Zn(t.dates[0],i())),T=k(()=>ft(J(Zn(t.dates.at(-1),i())))),b=k(()=>v()??(o()?l():void 0)),w=k(()=>{let S=[],A=[];for(let F of d())if(F.allDay&&on(F,a(R),a(T),a(b))){let E=Kt(F,a(R),a(T));vt(F.display)?A.push(E):S.push(E)}Vn(A,s());let B=Vn(S,s());return[S,A,B]}),p=k(()=>Mn(a(w),3)),D=k(()=>a(p)[0]),C=k(()=>a(p)[1]),x=k(()=>a(p)[2]);function H(){$n($,t.dates)}Ze(()=>{a(D),Z(H)});let I=k(()=>f().map(S=>{let A;return S&&S.allDay&&on(S,a(R),a(T),v())?(A=Kt(S,a(R),a(T)),Vn([A],s())):A=null,A}));var N=Ce();Ve("resize",mt,H);var z=se(N);we(z,17,()=>t.dates,Ie,(S,A,B)=>{ze(pd(S,{get date(){return a(A)},get chunks(){return a(D)},get bgChunks(){return a(C)},get longChunks(){return a(x)},get iChunks(){return a(I)},get resource(){return v()}}),(F,E)=>$[E]=F,F=>$==null?void 0:$[F],()=>[B])}),L(e,N),ve(),r()}var Cd=W('<div role="columnheader"><time></time></div>'),xd=W("<div><div><!> <div></div></div></div>"),Sd=W("<div><!> <div></div></div> <!> <!>",1);function No(e,t){fe(t,!1);const[n,r]=$e(),i=()=>u(y,"$theme",n),o=()=>u(v,"$_viewDates",n),l=()=>u(m,"$_today",n),d=()=>u(c,"$_intlDayHeaderAL",n),s=()=>u(h,"$_intlDayHeader",n),f=()=>u(_,"$allDaySlot",n);let{_viewDates:v,_intlDayHeader:h,_intlDayHeaderAL:c,_today:m,allDaySlot:_,theme:y}=be("state");pn();var g=Sd(),$=se(g),R=X($);$r(R,{children:(D,C)=>{var x=Ce(),H=se(x);we(H,1,o,Ie,(I,N)=>{var z=Cd(),S=X(z);Ue(S,(A,B)=>K==null?void 0:K(A,B),()=>s().format(a(N))),q((A,B,F,E)=>{P(z,1,`${i().day??""} ${A??""}${B??""}`),We(S,"datetime",F),We(S,"aria-label",E)},[()=>{var A;return(A=i().weekdays)==null?void 0:A[a(N).getUTCDay()]},()=>Pe(a(N),l())?" "+i().today:"",()=>lt(a(N),10),()=>d().format(a(N))],cn),L(I,z)}),L(D,x)},$$slots:{default:!0}});var T=ie(R,2),b=ie($,2);{var w=D=>{var C=xd(),x=X(C),H=X(x);$r(H,{children:(N,z)=>{ea(N,{get dates(){return o()}})},$$slots:{default:!0}});var I=ie(H,2);q(()=>{P(C,1,i().allDay),P(x,1,i().content),P(I,1,i().hiddenScroll)}),L(D,C)};oe(b,D=>{f()&&D(w)})}var p=ie(b,2);Po(p,{children:(D,C)=>{var x=Ce(),H=se(x);we(H,1,o,Ie,(I,N)=>{Io(I,{get date(){return a(N)}})}),L(D,x)},$$slots:{default:!0}}),q(()=>{P($,1,i().header),P(T,1,i().hiddenScroll)}),L(e,g),ve(),r()}const Fo={createOptions(e){e.buttonText.timeGridDay="day",e.buttonText.timeGridWeek="week",e.view="timeGridWeek",e.views.timeGridDay={buttonText:ti,component:No,dayHeaderFormat:{weekday:"long"},duration:{days:1},theme:At("ec-time-grid ec-day-view"),titleFormat:{year:"numeric",month:"long",day:"numeric"}},e.views.timeGridWeek={buttonText:ni,component:No,duration:{weeks:1},theme:At("ec-time-grid ec-week-view")}},createStores(e){e._slotTimeLimits=vd(e),e._times=fd(e)}};var Rd=W("<span></span>");function Oo(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(f,"$resourceLabelContent",n),o=()=>u(h,"$_intlDayHeaderAL",n),l=()=>u(v,"$resourceLabelDidMount",n);let d=Be(t,"date",3,void 0),s=Be(t,"setLabel",3,void 0),{resourceLabelContent:f,resourceLabelDidMount:v,_intlDayHeaderAL:h}=be("state"),c=He(void 0),m=k(()=>i()?Se(i())?i()({resource:t.resource,date:d()?je(d()):void 0}):i():t.resource.title),_=He(void 0);Ze(()=>{a(m),Z(()=>{d()?j(_,o().format(d())+", "+a(c).innerText):s()&&(j(_,void 0),s()(a(c).innerText))})}),Bn(()=>{Se(l())&&l()({resource:t.resource,date:d()?je(d()):void 0,el:a(c)})});var y=Rd();ze(y,g=>j(c,g),()=>a(c)),Ue(y,(g,$)=>K==null?void 0:K(g,$),()=>a(m)),q(()=>We(y,"aria-label",a(_))),L(e,y),ve(),r()}var Ld=W("<div><time></time></div>"),Ad=W("<div><!></div>"),Md=W('<div role="columnheader"><!></div>'),Hd=W('<div role="columnheader"><time></time></div>'),Pd=W("<div></div>"),Id=W("<div><!> <!></div>"),Nd=W("<div></div>"),Fd=W("<div><!></div>"),Od=W("<div><div><!> <div></div></div></div>"),Ud=W("<div></div>"),Wd=W("<div><!> <div></div></div> <!> <!>",1);function Uo(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(c,"$datesAboveResources",n),o=()=>u(_,"$_viewDates",n),l=()=>u(y,"$_viewResources",n),d=()=>u(T,"$theme",n),s=()=>u(m,"$_today",n),f=()=>u($,"$_intlDayHeaderAL",n),v=()=>u(g,"$_intlDayHeader",n),h=()=>u(R,"$allDaySlot",n);let{datesAboveResources:c,_today:m,_viewDates:_,_viewResources:y,_intlDayHeader:g,_intlDayHeaderAL:$,allDaySlot:R,theme:T}=be("state"),b=k(()=>i()?[o(),l()]:[l(),o()]),w=tn([]);var p=Wd(),D=se(p),C=X(D);$r(C,{children:(z,S)=>{var A=Ce(),B=se(A);we(B,17,()=>a(b)[0],Ie,(F,E,O)=>{var U=Id(),Q=X(U);{var le=ke=>{var me=Ld(),ue=X(me);Ue(ue,(ee,De)=>K==null?void 0:K(ee,De),()=>v().format(a(E))),q((ee,De,ge,Ae)=>{P(me,1,`${d().day??""} ${ee??""}${De??""}`),We(ue,"datetime",ge),We(ue,"aria-label",Ae)},[()=>{var ee;return(ee=d().weekdays)==null?void 0:ee[a(E).getUTCDay()]},()=>Pe(a(E),s())?" "+d().today:"",()=>lt(a(E),10),()=>f().format(a(E))]),L(ke,me)},M=ke=>{var me=Ad(),ue=X(me);Oo(ue,{get resource(){return a(E)},setLabel:ee=>w[O]=ee.detail+", "}),q(()=>P(me,1,d().day)),L(ke,me)};oe(Q,ke=>{i()?ke(le):ke(M,!1)})}var ae=ie(Q,2);{var _e=ke=>{var me=Pd();we(me,21,()=>a(b)[1],Ie,(ue,ee)=>{var De=Ce(),ge=se(De);{var Ae=Te=>{var te=Md(),he=X(te);Oo(he,{get resource(){return a(ee)},get date(){return a(E)}}),q(()=>P(te,1,d().day)),L(Te,te)},Me=Te=>{var te=Hd(),he=X(te);Ue(he,(Re,G)=>K==null?void 0:K(Re,G),()=>v().format(a(ee))),q((Re,G,re,pe)=>{P(te,1,`${d().day??""} ${Re??""}${G??""}`),We(he,"datetime",re),We(he,"aria-label",`${w[O]??""}${pe??""}`)},[()=>{var Re;return(Re=d().weekdays)==null?void 0:Re[a(ee).getUTCDay()]},()=>Pe(a(ee),s())?" "+d().today:"",()=>lt(a(ee),10),()=>f().format(a(ee))]),L(Te,te)};oe(ge,Te=>{i()?Te(Ae):Te(Me,!1)})}L(ue,De)}),q(()=>P(me,1,d().days)),L(ke,me)};oe(ae,ke=>{a(b)[1].length>1&&ke(_e)})}q(()=>P(U,1,d().resource)),L(F,U)}),L(z,A)},$$slots:{default:!0}});var x=ie(C,2),H=ie(D,2);{var I=z=>{var S=Od(),A=X(S),B=X(A);$r(B,{children:(E,O)=>{var U=Ce(),Q=se(U);{var le=ae=>{var _e=Ce(),ke=se(_e);we(ke,1,o,Ie,(me,ue)=>{var ee=Nd();we(ee,5,l,Ie,(De,ge)=>{const Ae=k(()=>[a(ue)]);ea(De,{get dates(){return a(Ae)},get resource(){return a(ge)}})}),q(()=>P(ee,1,d().resource)),L(me,ee)}),L(ae,_e)},M=ae=>{var _e=Ce(),ke=se(_e);we(ke,1,l,Ie,(me,ue)=>{var ee=Fd(),De=X(ee);ea(De,{get dates(){return o()},get resource(){return a(ue)}}),q(()=>P(ee,1,d().resource)),L(me,ee)}),L(ae,_e)};oe(Q,ae=>{i()?ae(le):ae(M,!1)})}L(E,U)},$$slots:{default:!0}});var F=ie(B,2);q(()=>{P(S,1,d().allDay),P(A,1,d().content),P(F,1,d().hiddenScroll)}),L(z,S)};oe(H,z=>{h()&&z(I)})}var N=ie(H,2);Po(N,{children:(z,S)=>{var A=Ce(),B=se(A);we(B,17,()=>a(b)[0],Ie,(F,E)=>{var O=Ud();we(O,21,()=>a(b)[1],Ie,(U,Q)=>{const le=k(()=>i()?a(E):a(Q)),M=k(()=>i()?a(Q):a(E));Io(U,{get date(){return a(le)},get resource(){return a(M)}})}),q(()=>P(O,1,d().resource)),L(F,O)}),L(z,A)},$$slots:{default:!0}}),q(()=>{P(D,1,d().header),P(x,1,d().hiddenScroll)}),L(e,p),ve(),r()}const zd={createOptions(e){e.datesAboveResources=!1,e.buttonText.resourceTimeGridDay="resources",e.buttonText.resourceTimeGridWeek="resources",e.view="resourceTimeGridWeek",e.views.resourceTimeGridDay={buttonText:ti,component:Uo,duration:{days:1},theme:At("ec-time-grid ec-resource-day-view")},e.views.resourceTimeGridWeek={buttonText:ni,component:Uo,duration:{weeks:1},theme:At("ec-time-grid ec-resource-week-view")}},createStores(e){"_times"in e||Fo.createStores(e),"_viewResources"in e||(e._viewResources=po(e))}};function Bd(e){return it([e.slotMinTime,e.slotMaxTime,e.flexibleSlotTimeLimits,e._viewDates,e._filteredEvents],([t,n,r,i,o])=>{let l={};for(let d of i)l[d.getTime()]=xo(t,n,r,[d],o);return l})}function Gd(e){return it([e._viewDates,e.slotDuration,e.slotLabelInterval,e._dayTimeLimits,e._intlSlotLabel],([t,n,r,i,o])=>{let l={};for(let d of t){let s=d.getTime();l[s]=s in i?Co(d,n,r,i[s],o):[]}return l})}function Yd(e){return it(e.resources,t=>t.some(n=>an(n).children.length))}var qd=W("<span></span>");function Vd(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(d,"$resourceLabelContent",n),o=()=>u(s,"$resourceLabelDidMount",n);let l=Be(t,"date",3,void 0),{resourceLabelContent:d,resourceLabelDidMount:s}=be("state"),f=He(void 0),v=k(()=>i()?Se(i())?i()({resource:t.resource,date:l()?je(l()):void 0}):i():t.resource.title);Bn(()=>{Se(o())&&o()({resource:t.resource,date:l()?je(l()):void 0,el:a(f)})});var h=qd();ze(h,c=>j(f,c),()=>a(f)),Ue(h,(c,m)=>K==null?void 0:K(c,m),()=>a(v)),L(e,h),ve(),r()}function Xd(e,t,n,r,i){j(t,!a(t)),a(n).expanded=a(t),r(a(n).children,a(t)),i.update(Jr)}var Kd=W("<span></span>"),Zd=W("<button><!></button>"),Jd=W("<!> <span><!></span>",1);function Qd(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(l,"$theme",n);let{resources:o,theme:l}=be("state"),d=He({}),s=He(!0);Fn(()=>{j(d,an(t.resource)),j(s,a(d).expanded,!0)});function f(y,g){for(let $ of y){let R=an($);R.hidden=!g,R.expanded&&f(R.children,g)}}var v=Jd(),h=se(v);we(h,17,()=>Array(a(d).level),Ie,(y,g)=>{var $=Kd();q(()=>P($,1,i().expander)),L(y,$)});var c=ie(h,2),m=X(c);{var _=y=>{var g=Zd();g.__click=[Xd,s,d,f,o];var $=X(g);{var R=b=>{var w=Ja("−");L(b,w)},T=b=>{var w=Ja("+");L(b,w)};oe($,b=>{a(s)?b(R):b(T,!1)})}q(()=>P(g,1,i().button)),L(y,g)};oe(m,y=>{var g;(g=a(d).children)!=null&&g.length&&y(_)})}q(()=>P(c,1,i().expander)),L(e,v),ve(),r()}qt(["click"]);var jd=W('<div role="rowheader"><!> <!></div>'),ec=W("<div><div></div> <div></div></div>");function tc(e,t){fe(t,!1);const[n,r]=$e(),i=()=>u(m,"$_bodyEl",n),o=()=>u($,"$theme",n),l=()=>u(c,"$_headerHeight",n),d=()=>u(y,"$_sidebarEl",n),s=()=>u(h,"$_viewResources",n),f=()=>u(_,"$_resHs",n),v=()=>u(g,"$_nestedResources",n);let{_viewResources:h,_headerHeight:c,_bodyEl:m,_resHs:_,_sidebarEl:y,_nestedResources:g,theme:$}=be("state");function R(p){i().scrollBy({top:p.deltaY<0?-30:30})}pn();var T=ec(),b=X(T),w=ie(b,2);we(w,5,s,Ie,(p,D)=>{var C=jd(),x=X(C);{var H=N=>{Qd(N,{get resource(){return a(D)}})};oe(x,N=>{v()&&N(H)})}var I=ie(x,2);Vd(I,{get resource(){return a(D)}}),q(N=>{P(C,1,o().resource),Vt(C,`flex-basis: ${N??""}px`)},[()=>gt(f().get(a(D))??0,34)],cn),L(p,C)}),ze(w,p=>de(y,p),()=>d()),q(()=>{P(T,1,o().sidebar),P(b,1,o().sidebarTitle),Vt(b,`flex-basis: ${l()??""}px`),P(w,1,o().content)}),Ve("wheel",w,R),L(e,T),ve(),r()}var nc=W('<div role="columnheader"><time></time></div>'),rc=W("<div><time></time></div> <div></div>",1),ic=W('<div role="columnheader"><time></time></div>'),ac=W("<div><!></div>"),oc=W('<div><div role="row"></div> <div></div></div>');function lc(e,t){fe(t,!1);const[n,r]=$e(),i=()=>u(b,"$theme",n),o=()=>u(c,"$_headerEl",n),l=()=>u(R,"$_viewDates",n),d=()=>u($,"$_today",n),s=()=>u(T,"$slotDuration",n),f=()=>u(y,"$_intlDayHeaderAL",n),v=()=>u(_,"$_intlDayHeader",n),h=()=>u(g,"$_dayTimes",n);let{_headerEl:c,_headerHeight:m,_intlDayHeader:_,_intlDayHeaderAL:y,_dayTimes:g,_today:$,_viewDates:R,slotDuration:T,theme:b}=be("state");pn();var w=oc(),p=X(w);we(p,5,l,Ie,(C,x)=>{var H=ac(),I=X(H);{var N=S=>{var A=rc(),B=se(A),F=X(B);Ue(F,(O,U)=>K==null?void 0:K(O,U),()=>v().format(a(x)));var E=ie(B,2);we(E,5,()=>h()[a(x).getTime()],Ie,(O,U)=>{var Q=nc(),le=X(Q);Ue(le,(M,ae)=>K==null?void 0:K(M,ae),()=>a(U)[1]),q(()=>{P(Q,1,`${i().time??""}${a(U)[2]?"":" "+i().minor}`),We(le,"datetime",a(U)[0])}),L(O,Q)}),q((O,U)=>{P(B,1,i().dayHead),We(F,"datetime",O),We(F,"aria-label",U),P(E,1,i().times)},[()=>lt(a(x),10),()=>f().format(a(x))],cn),L(S,A)},z=S=>{var A=ic(),B=X(A);Ue(B,(F,E)=>K==null?void 0:K(F,E),()=>v().format(a(x))),q((F,E)=>{P(A,1,i().time),We(B,"datetime",F),We(B,"aria-label",E)},[()=>lt(a(x),10),()=>f().format(a(x))],cn),L(S,A)};oe(I,S=>{Xe(s())?S(N):S(z,!1)})}q((S,A)=>P(H,1,`${i().day??""} ${S??""}${A??""}`),[()=>{var S;return(S=i().weekdays)==null?void 0:S[a(x).getUTCDay()]},()=>Pe(a(x),d())?" "+i().today:""],cn),L(C,H)});var D=ie(p,2);ze(w,C=>de(c,C),()=>o()),Ue(w,(C,x)=>Rt==null?void 0:Rt(C,x),()=>()=>de(m,o().clientHeight)),q(()=>{P(w,1,i().header),P(p,1,i().days),P(D,1,i().hiddenScroll)}),L(e,w),ve(),r()}function ta(e,t,n,r){let i={},o=[];if(e.length){ei(e);let l=Xe(r),d;for(let s of e){let f;if(l){let v=0;for(let h=0;h<t.length;++h){let c=Jn(n,t[h]),m=xe(J(t[h]),c.min),_=xe(J(t[h]),c.max);if(s.date){if(s.end<=m){s.end=f;break}let y=t[h].getTime();if(i[y]?i[y].push(s):i[y]=[s],s.end>_)v+=_-m;else{v+=s.end-m;break}}else if(s.start<_&&s.end>m)if(s.date=t[h],s.start<m&&(s.start=m),s.offset=(s.start-m)/1e3/l,s.end>_)v+=_-s.start;else{v+=s.end-s.start||l*1e3;break}f=_}s.slots=v/1e3/l}else{let v=0;for(let h=0;h<t.length;++h){let c=t[h],m=ft(J(c));if(!s.date)s.start<m&&(s.date=c,s.start<c&&(s.start=c),++v);else{if(s.end<=c){s.end=f;break}let _=c.getTime();i[_]?i[_].push(s):i[_]=[s],++v}f=m}s.days=v}s.date&&(d&&Pe(d.date,s.date)&&(s.prev=d),d=s,o.push(s))}}return[o,i]}function sc(e,t,n,r,i){e.top=0,e.bottom=r;let o=1,l=e.date.getTime();n=(n==null?void 0:n[l])??[];let d=[...t,...n];d.sort((s,f)=>(s.top??0)-(f.top??0)||s.start-f.start||f.event.allDay-s.event.allDay);for(let s of d)if(s!==e&&(i||e.start<s.end&&e.end>s.start)&&e.top<s.bottom&&e.bottom>s.top){let f=s.bottom-e.top+1;o+=f,e.top+=f,e.bottom+=f}return o}function Jn(e,t){return e[t.getTime()]??{min:tt(0),max:tt(0)}}function li(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(v,"$slotWidth",n),o=()=>u(f,"$slotDuration",n);let l=Be(t,"dayChunks",19,()=>[]),d=Be(t,"longChunks",19,()=>({})),s=Be(t,"resource",3,void 0),{slotDuration:f,slotWidth:v}=be("state"),h=He(void 0),c=He(tn(Xn(t.chunk.event.display)?1:0)),m=k(()=>t.chunk.event),_=k(()=>"slots"in t.chunk?t.chunk.slots*i():t.chunk.days*100),y=k(()=>w=>{if("slots"in t.chunk){let D=t.chunk.offset*i();w.left=`${D}px`,w.width=`${a(_)}px`}else w.width=`${a(_)}%`;let p=a(c);if(a(m)._margin){let[D,C]=a(m)._margin;s()===C&&(p=D)}return w["margin-top"]=`${p}px`,w});function g(){if(!a(h))return 0;let w=Yn(a(h));return j(c,sc(t.chunk,l(),d(),w,!Xe(o())),!0),a(c)+w}var $=Ce(),R=se($);{var T=w=>{ii(w,{get chunk(){return t.chunk},get styles(){return a(y)},axis:"x",forceMargin:()=>[a(c),s()],get el(){return a(h)},set el(p){j(h,p,!0)}})};oe(R,w=>{a(_)>0&&w(T)})}L(e,$);var b=ve({reposition:g});return r(),b}var uc=W("<!> <!> <!> <!>",1),dc=W('<div role="cell"><div><!></div></div>');function cc(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(T,"$_today",n),o=()=>u(m,"$highlightedDates",n),l=()=>u($,"$validRange",n),d=()=>u(b,"$_dayTimeLimits",n),s=()=>u(_,"$slotDuration",n),f=()=>u(y,"$slotWidth",n),v=()=>u(g,"$theme",n),h=()=>u(R,"$_interaction",n);let c=Be(t,"iChunks",19,()=>[]),{highlightedDates:m,slotDuration:_,slotWidth:y,theme:g,validRange:$,_interaction:R,_today:T,_dayTimeLimits:b}=be("state"),w=He(void 0),p=[],D=k(()=>Pe(t.date,i())),C=k(()=>o().some(M=>Pe(M,t.date))),x=k(()=>Kn(t.date,l())),H=k(()=>Jn(d(),t.date)),I=k(()=>!Xe(s())),N=k(()=>a(I)?2:1),z=k(()=>t.chunks.filter(A)),S=k(()=>t.bgChunks.filter(M=>(!a(I)||M.event.allDay)&&A(M)));function A(M){return Pe(M.date,t.date)}function B(M,ae){return M-=Lt(a(w)).left,{allDay:a(I),date:a(I)?J(t.date):xe(xe(J(t.date),a(H).min),s(),fo(M/f())),resource:t.resource,dayEl:a(w),disabled:a(x)}}Ze(()=>{Gn(a(w),B)});function F(){return gt(...$n(p,a(z)))}var E=dc();E.__pointerdown=function(...M){var ae,_e;(_e=(ae=h().action)==null?void 0:ae.select)==null||_e.apply(this,M)};var O=X(E),U=X(O);{var Q=M=>{var ae=uc(),_e=se(ae);we(_e,17,()=>a(S),ge=>ge.event,(ge,Ae)=>{li(ge,{get chunk(){return a(Ae)}})});var ke=ie(_e,2);{var me=ge=>{li(ge,{get chunk(){return c()[a(N)]}})};oe(ke,ge=>{c()[a(N)]&&A(c()[a(N)])&&ge(me)})}var ue=ie(ke,2);we(ue,19,()=>a(z),ge=>ge.event,(ge,Ae,Me)=>{ze(li(ge,{get chunk(){return a(Ae)},get dayChunks(){return a(z)},get longChunks(){return t.longChunks},get resource(){return t.resource}}),(Te,te)=>p[te]=Te,Te=>p==null?void 0:p[Te],()=>[a(Me)])});var ee=ie(ue,2);{var De=ge=>{li(ge,{get chunk(){return c()[0]},get resource(){return t.resource}})};oe(ee,ge=>{c()[0]&&A(c()[0])&&ge(De)})}L(M,ae)};oe(U,M=>{a(x)||M(Q)})}ze(E,M=>j(w,M),()=>a(w)),q((M,ae)=>{P(E,1,`${v().day??""} ${M??""}${a(D)?" "+v().today:""}${a(C)?" "+v().highlight:""}${a(x)?" "+v().disabled:""}`),Vt(E,`flex-grow: ${ae??""}`),P(O,1,v().events)},[()=>{var M;return(M=v().weekdays)==null?void 0:M[t.date.getUTCDay()]},()=>a(I)?null:vo((Xe(a(H).max)-Xe(a(H).min))/Xe(s()))]),L(e,E);var le=ve({reposition:F});return r(),le}qt(["pointerdown"]);var fc=W('<div role="row"></div>');function vc(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(c,"$_viewDates",n),o=()=>u(T,"$validRange",n),l=()=>u(g,"$_dayTimeLimits",n),d=()=>u(m,"$_filteredEvents",n),s=()=>u($,"$slotDuration",n),f=()=>u(_,"$_iEvents",n),v=()=>u(y,"$_resHs",n),h=()=>u(R,"$theme",n);let{_viewDates:c,_filteredEvents:m,_iEvents:_,_resHs:y,_dayTimeLimits:g,slotDuration:$,theme:R,validRange:T}=be("state"),b=[],w=He(0),p=k(()=>{let O=J(Zn(i()[0],o())),U=J(Zn(i().at(-1),o())),Q=Jn(l(),O);return xe(O,Q.min),Q=Jn(l(),U),Q.max.seconds>mr?xe(U,Q.max):ft(U),[O,U]}),D=k(()=>Mn(a(p),2)),C=k(()=>a(D)[0]),x=k(()=>a(D)[1]),H=k(()=>{let O=[],U=[],Q;for(let le of d())if(on(le,a(C),a(x),t.resource)){let M=Kt(le,a(C),a(x));vt(le.display)?U.push(M):O.push(M)}return[U]=ta(U,i(),l(),s()),[O,Q]=ta(O,i(),l(),s()),[O,U,Q]}),I=k(()=>Mn(a(H),3)),N=k(()=>a(I)[0]),z=k(()=>a(I)[1]),S=k(()=>a(I)[2]),A=k(()=>f().map(O=>{let U;return O&&on(O,a(C),a(x),t.resource)?(U=Kt(O,a(C),a(x)),[[U]]=ta([U],i(),l(),s())):U=null,U}));function B(){j(w,vo(gt(...$n(b,i())))+10),v().set(t.resource,a(w)),de(y,v())}var F=fc();we(F,5,i,Ie,(O,U,Q)=>{ze(cc(O,{get date(){return a(U)},get resource(){return t.resource},get chunks(){return a(N)},get bgChunks(){return a(z)},get longChunks(){return a(S)},get iChunks(){return a(A)}}),(le,M)=>b[M]=le,le=>b==null?void 0:b[le],()=>[Q])}),q(O=>{P(F,1,h().days),Vt(F,`flex-basis: ${O??""}px`)},[()=>gt(a(w),34)]),L(e,F);var E=ve({reposition:B});return r(),E}var hc=W("<div></div>"),_c=W("<div><div><div></div> <!></div></div>");function mc(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(x,"$_dayTimeLimits",n),o=()=>u(z,"$_viewDates",n),l=()=>u($,"$_bodyEl",n),d=()=>u(S,"$scrollTime",n),s=()=>u(A,"$slotDuration",n),f=()=>u(B,"$slotWidth",n),v=()=>u(I,"$_resHs",n),h=()=>u(N,"$_viewResources",n),c=()=>u(p,"$_filteredEvents",n),m=()=>u(w,"$_headerEl",n),_=()=>u(D,"$_sidebarEl",n),y=()=>u(F,"$theme",n),g=()=>u(C,"$_dayTimes",n);let{_bodyEl:$,_bodyHeight:R,_bodyWidth:T,_bodyScrollLeft:b,_headerEl:w,_filteredEvents:p,_sidebarEl:D,_dayTimes:C,_dayTimeLimits:x,_recheckScrollable:H,_resHs:I,_viewResources:N,_viewDates:z,scrollTime:S,slotDuration:A,slotWidth:B,theme:F}=be("state"),E=[];function O(){let me=Jn(i(),o()[0]);Qe($,Z(l).scrollLeft=(Xe(d())-Xe(me.min))/Xe(s())*f(),Z(l)),de(b,l().scrollLeft)}Ze(()=>{o(),d(),Z(O)});function U(){v().clear(),$n(E,h())}Ze(()=>{c(),h(),Z(U)});function Q(){Qe(w,Z(m).scrollLeft=l().scrollLeft,Z(m)),Qe(D,Z(_).scrollTop=l().scrollTop,Z(_)),de(b,l().scrollLeft)}function le(){de(R,l().clientHeight),de(T,l().clientWidth),de(H,!0)}var M=_c();Ve("resize",mt,U);var ae=X(M),_e=X(ae);we(_e,5,o,Ie,(me,ue)=>{var ee=Ce(),De=se(ee);we(De,1,()=>g()[a(ue).getTime()],Ie,(ge,Ae)=>{var Me=hc();q(()=>P(Me,1,`${y().line??""}${a(Ae)[2]?"":" "+y().minor}`)),L(ge,Me)}),L(me,ee)});var ke=ie(_e,2);we(ke,1,h,Ie,(me,ue,ee)=>{ze(vc(me,{get resource(){return a(ue)}}),(De,ge)=>E[ge]=De,De=>E==null?void 0:E[De],()=>[ee])}),ze(M,me=>de($,me),()=>l()),Ue(M,(me,ue)=>Rt==null?void 0:Rt(me,ue),()=>le),q(()=>{P(M,1,y().body),P(ae,1,y().content),P(_e,1,y().lines)}),Ve("scroll",M,Q),L(e,M),ve(),r()}var gc=W("<div></div>");function yc(e,t){fe(t,!0);const[n,r]=$e(),i=()=>u(x,"$_viewDates",n),o=()=>u(p,"$_dayTimeLimits",n),l=()=>u(C,"$_today",n),d=()=>u(D,"$_now",n),s=()=>u(y,"$slotDuration",n),f=()=>u(g,"$slotWidth",n),v=()=>u(b,"$_bodyScrollLeft",n),h=()=>u(T,"$_bodyWidth",n),c=()=>u($,"$theme",n),m=()=>u(w,"$_headerHeight",n),_=()=>u(R,"$_bodyHeight",n);let{slotDuration:y,slotWidth:g,theme:$,_bodyHeight:R,_bodyWidth:T,_bodyScrollLeft:b,_headerHeight:w,_dayTimeLimits:p,_now:D,_today:C,_viewDates:x}=be("state"),H=k(()=>{let S=0;for(let B=0;B<i().length;++B){let F=Jn(o(),i()[B]);if(Pe(i()[B],l())){let E=xe(J(i()[B]),F.min),O=xe(J(i()[B]),F.max);if(d()>=E&&d()<=O){S+=(d()-E)/1e3;break}else return null}else S+=F.max.seconds-F.min.seconds}let A=s().seconds;return S/A*f()-v()});var I=Ce(),N=se(I);{var z=S=>{var A=gc();let B;q(()=>{P(A,1,c().nowIndicator),B=Vt(A,"",B,{top:`${m()+2}px`,left:`${a(H)??""}px`,height:`${_()-1}px`})}),L(S,A)};oe(N,S=>{a(H)!==null&&a(H)>=3&&a(H)<=h()-3&&S(z)})}L(e,I),ve(),r()}var $c=W("<div><!> <div><!> <!> <!></div></div>");function na(e,t){fe(t,!1);const[n,r]=$e(),i=()=>u(d,"$theme",n),o=()=>u(l,"$nowIndicator",n);let{nowIndicator:l,theme:d}=be("state");pn();var s=$c(),f=X(s);tc(f,{});var v=ie(f,2),h=X(v);lc(h,{});var c=ie(h,2);mc(c,{});var m=ie(c,2);{var _=y=>{yc(y,{})};oe(m,y=>{o()&&y(_)})}q(()=>{P(s,1,i().container),P(v,1,i().main)}),L(e,s),ve(),r()}const bc={createOptions(e){e.buttonText.resourceTimelineDay="timeline",e.buttonText.resourceTimelineWeek="timeline",e.buttonText.resourceTimelineMonth="timeline",e.theme.expander="ec-expander",e.theme.main="ec-main",e.theme.times="ec-times",e.theme.container="ec-container",e.view="resourceTimelineWeek",e.views.resourceTimelineDay={buttonText:ti,component:na,displayEventEnd:!1,dayHeaderFormat:{weekday:"long"},duration:{days:1},slotDuration:"01:00",theme:At("ec-timeline ec-resource-day-view"),titleFormat:{year:"numeric",month:"long",day:"numeric"}},e.views.resourceTimelineWeek={buttonText:ni,component:na,displayEventEnd:!1,duration:{weeks:1},slotDuration:"01:00",theme:At("ec-timeline ec-resource-week-view")},e.views.resourceTimelineMonth={buttonText:Xi,component:na,displayEventEnd:!1,dayHeaderFormat:{weekday:"short",day:"numeric"},duration:{months:1},slotDuration:{days:1},theme:At("ec-timeline ec-resource-month-view"),titleFormat:{year:"numeric",month:"long"}}},createStores(e){"_viewResources"in e||(e._viewResources=po(e)),e._bodyHeight=Ye(0),e._bodyWidth=Ye(0),e._bodyScrollLeft=Ye(0),e._headerEl=Ye(void 0),e._headerHeight=Ye(0),e._dayTimeLimits=Bd(e),e._dayTimes=Gd(e),e._nestedResources=Yd(e),e._resHs=Ye(new Map),e._sidebarEl=Ye(void 0)}};function wc(e,t,n){return ls(wu,{target:e,props:{plugins:t,options:n}})}function Dc(e){return us(e)}function Ec(e,t){return wc(e,[Yu,nd,cd,zd,bc,Fo],t)}return rt.create=Ec,rt.destroy=Dc,Object.defineProperty(rt,Symbol.toStringTag,{value:"Module"}),rt}({});
//# sourceMappingURL=event-calendar.min.js.map
