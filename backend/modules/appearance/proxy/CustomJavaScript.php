<?php
namespace Bookly\Backend\Modules\Appearance\Proxy;

use Bookly\Lib;

/**
 * @method static void renderNextButton( string $step )  Render editable Next button with custom JavaScript
 * @method static void renderTimeSlotsJS()  Render elements for editing custom JavaScript for time slots
 * @method static void renderEditor()  Render dialog for editing Custom JavaScript
 */
abstract class CustomJavaScript extends Lib\Base\Proxy
{
}