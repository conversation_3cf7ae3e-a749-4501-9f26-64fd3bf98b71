/*!
  * Bootstrap v4.6.1 (https://getbootstrap.com/)
  * Copyright 2011-2022 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery")):"function"==typeof define&&define.amd?define(["exports","jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap={},t.jQuery)}(this,function(t,e){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var p=n(e);function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function a(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function s(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function l(o){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var e,n,i;e=o,i=r[n=t],n in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(o,t,Object.getOwnPropertyDescriptor(r,t))})}return o}var o="transitionend";function r(t){var e=this,n=!1;return p.default(this).one(m.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||m.triggerTransitionEnd(e)},t),this}var m={TRANSITION_END:"booklyTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e,n=t.getAttribute("data-target");n&&"#"!==n||(n=(e=t.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(n)?n:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var e=p.default(t).css("transition-duration"),n=p.default(t).css("transition-delay"),i=parseFloat(e),o=parseFloat(n);return i||o?(e=e.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(e)+parseFloat(n))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){p.default(t).trigger(o)},supportsTransitionEnd:function(){return Boolean(o)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var o=n[i],r=e[i],a=r&&m.isElement(r)?"element":(s=r,{}.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase());if(!new RegExp(o).test(a))throw new Error(t.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+o+'".')}var s},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"!=typeof t.getRootNode)return t instanceof ShadowRoot?t:t.parentNode?m.findShadowRoot(t.parentNode):null;var e=t.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if("undefined"==typeof p.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=p.default.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};m.jQueryDetection(),p.default.fn.booklyEmulateTransitionEnd=r,p.default.event.special[m.TRANSITION_END]={bindType:o,delegateType:o,handle:function(t){if(p.default(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var u="alert",f="bs.alert",d="."+f,c=p.default.fn[u],h={CLOSE:"close"+d,CLOSED:"closed"+d,CLICK_DATA_API:"click"+d+".data-api"},g="alert",_="bookly-fade",v="bookly-show",y=function(){function i(t){this._element=t}var t=i.prototype;return t.close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},t.dispose=function(){p.default.removeData(this._element,f),this._element=null},t._getRootElement=function(t){var e=m.getSelectorFromElement(t),n=!1;return e&&(n=document.querySelector(e)),n=n||p.default(t).closest("."+g)[0]},t._triggerCloseEvent=function(t){var e=p.default.Event(h.CLOSE);return p.default(t).trigger(e),e},t._removeElement=function(e){var t,n=this;p.default(e).removeClass(v),p.default(e).hasClass(_)?(t=m.getTransitionDurationFromElement(e),p.default(e).one(m.TRANSITION_END,function(t){return n._destroyElement(e,t)}).booklyEmulateTransitionEnd(t)):this._destroyElement(e)},t._destroyElement=function(t){p.default(t).detach().trigger(h.CLOSED).remove()},i._jQueryInterface=function(n){return this.each(function(){var t=p.default(this),e=t.data(f);e||(e=new i(this),t.data(f,e)),"close"===n&&e[n](this)})},i._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},a(i,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),i}();p.default(document).on(h.CLICK_DATA_API,'[data-dismiss="alert"]',y._handleDismiss(new y)),p.default.fn[u]=y._jQueryInterface,p.default.fn[u].Constructor=y,p.default.fn[u].noConflict=function(){return p.default.fn[u]=c,y._jQueryInterface};var b="button",E="bs.button",w=p.default.fn[b],T="active",C='[data-toggle^="button"]',S='input:not([type="hidden"])',D=".btn",I=function(){function o(t){this._element=t,this.shouldAvoidTriggerChange=!1}var t=o.prototype;return t.toggle=function(){var t,e,n=!0,i=!0,o=p.default(this._element).closest('[data-toggle="buttons"]')[0];!o||(t=this._element.querySelector(S))&&("radio"===t.type&&(t.checked&&this._element.classList.contains(T)?n=!1:(e=o.querySelector(".active"))&&p.default(e).removeClass(T)),n&&("checkbox"!==t.type&&"radio"!==t.type||(t.checked=!this._element.classList.contains(T)),this.shouldAvoidTriggerChange||p.default(t).trigger("change")),t.focus(),i=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(i&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(T)),n&&p.default(this._element).toggleClass(T))},t.dispose=function(){p.default.removeData(this._element,E),this._element=null},o._jQueryInterface=function(n,i){return this.each(function(){var t=p.default(this),e=t.data(E);e||(e=new o(this),t.data(E,e)),e.shouldAvoidTriggerChange=i,"toggle"===n&&e[n]()})},a(o,null,[{key:"VERSION",get:function(){return"4.6.1"}}]),o}();p.default(document).on("click.bs.button.data-api",C,function(t){var e=t.target,n=e;if(p.default(e).hasClass("btn")||(e=p.default(e).closest(D)[0]),!e||e.hasAttribute("disabled")||e.classList.contains("disabled"))t.preventDefault();else{var i=e.querySelector(S);if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void t.preventDefault();"INPUT"!==n.tagName&&"LABEL"===e.tagName||I._jQueryInterface.call(p.default(e),"toggle","INPUT"===n.tagName)}}).on("focus.bs.button.data-api blur.bs.button.data-api",C,function(t){var e=p.default(t.target).closest(D)[0];p.default(e).toggleClass("focus",/^focus(in)?$/.test(t.type))}),p.default(window).on("load.bs.button.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),e=0,n=t.length;e<n;e++){var i=t[e],o=i.querySelector(S);o.checked||o.hasAttribute("checked")?i.classList.add(T):i.classList.remove(T)}for(var r=0,a=(t=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;r<a;r++){var s=t[r];"true"===s.getAttribute("aria-pressed")?s.classList.add(T):s.classList.remove(T)}}),p.default.fn[b]=I._jQueryInterface,p.default.fn[b].Constructor=I,p.default.fn[b].noConflict=function(){return p.default.fn[b]=w,I._jQueryInterface};var A="carousel",N="bs.carousel",O="."+N,k=".data-api",L=p.default.fn[A],P={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},x={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},j="next",H="prev",R="left",M="right",F={SLIDE:"slide"+O,SLID:"slid"+O,KEYDOWN:"keydown"+O,MOUSEENTER:"mouseenter"+O,MOUSELEAVE:"mouseleave"+O,TOUCHSTART:"touchstart"+O,TOUCHMOVE:"touchmove"+O,TOUCHEND:"touchend"+O,POINTERDOWN:"pointerdown"+O,POINTERUP:"pointerup"+O,DRAG_START:"dragstart"+O,LOAD_DATA_API:"load"+O+k,CLICK_DATA_API:"click"+O+k},W="carousel",U="active",q="slide",B="carousel-item-right",K="carousel-item-left",Q="carousel-item-next",V="carousel-item-prev",Y="pointer-event",z=".active",X=".active.carousel-item",G=".carousel-item",$=".carousel-item img",J=".carousel-item-next, .carousel-item-prev",Z=".carousel-indicators",tt="[data-slide], [data-slide-to]",et='[data-ride="carousel"]',nt={TOUCH:"touch",PEN:"pen"},it=function(){function r(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(Z),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var t=r.prototype;return t.next=function(){this._isSliding||this._slide(j)},t.nextWhenVisible=function(){!document.hidden&&p.default(this._element).is(":visible")&&"hidden"!==p.default(this._element).css("visibility")&&this.next()},t.prev=function(){this._isSliding||this._slide(H)},t.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(J)&&(m.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},t.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},t.to=function(t){var e=this;this._activeElement=this._element.querySelector(X);var n=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)p.default(this._element).one(F.SLID,function(){return e.to(t)});else{if(n===t)return this.pause(),void this.cycle();var i=n<t?j:H;this._slide(i,this._items[t])}},t.dispose=function(){p.default(this._element).off(O),p.default.removeData(this._element,N),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},t._getConfig=function(t){return t=l(l({},P),t),m.typeCheckConfig(A,t,x),t},t._handleSwipe=function(){var t,e=Math.abs(this.touchDeltaX);e<=40||(t=e/this.touchDeltaX,(this.touchDeltaX=0)<t&&this.prev(),t<0&&this.next())},t._addEventListeners=function(){var e=this;this._config.keyboard&&p.default(this._element).on(F.KEYDOWN,function(t){return e._keydown(t)}),"hover"===this._config.pause&&p.default(this._element).on(F.MOUSEENTER,function(t){return e.pause(t)}).on(F.MOUSELEAVE,function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},t._addTouchEventListeners=function(){var t,e,n=this;this._touchSupported&&(t=function(t){n._pointerEvent&&nt[t.originalEvent.pointerType.toUpperCase()]?n.touchStartX=t.originalEvent.clientX:n._pointerEvent||(n.touchStartX=t.originalEvent.touches[0].clientX)},e=function(t){n._pointerEvent&&nt[t.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=t.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(t){return n.cycle(t)},500+n._config.interval))},p.default(this._element.querySelectorAll($)).on(F.DRAG_START,function(t){return t.preventDefault()}),this._pointerEvent?(p.default(this._element).on(F.POINTERDOWN,t),p.default(this._element).on(F.POINTERUP,e),this._element.classList.add(Y)):(p.default(this._element).on(F.TOUCHSTART,t),p.default(this._element).on(F.TOUCHMOVE,function(t){var e;(e=t).originalEvent.touches&&1<e.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=e.originalEvent.touches[0].clientX-n.touchStartX}),p.default(this._element).on(F.TOUCHEND,e)))},t._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},t._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(G)):[],this._items.indexOf(t)},t._getItemByDirection=function(t,e){var n=t===j,i=t===H,o=this._getItemIndex(e),r=this._items.length-1;if((i&&0===o||n&&o===r)&&!this._config.wrap)return e;var a=(o+(t===H?-1:1))%this._items.length;return-1==a?this._items[this._items.length-1]:this._items[a]},t._triggerSlideEvent=function(t,e){var n=this._getItemIndex(t),i=this._getItemIndex(this._element.querySelector(X)),o=p.default.Event(F.SLIDE,{relatedTarget:t,direction:e,from:i,to:n});return p.default(this._element).trigger(o),o},t._setActiveIndicatorElement=function(t){var e,n;this._indicatorsElement&&(e=[].slice.call(this._indicatorsElement.querySelectorAll(z)),p.default(e).removeClass(U),(n=this._indicatorsElement.children[this._getItemIndex(t)])&&p.default(n).addClass(U))},t._slide=function(t,e){var n,i,o,r,a,s=this,l=this._element.querySelector(X),u=this._getItemIndex(l),f=e||l&&this._getItemByDirection(t,l),d=this._getItemIndex(f),c=Boolean(this._interval),h=t===j?(n=K,i=Q,R):(n=B,i=V,M);f&&p.default(f).hasClass(U)?this._isSliding=!1:this._triggerSlideEvent(f,h).isDefaultPrevented()||l&&f&&(this._isSliding=!0,c&&this.pause(),this._setActiveIndicatorElement(f),o=p.default.Event(F.SLID,{relatedTarget:f,direction:h,from:u,to:d}),p.default(this._element).hasClass(q)?(p.default(f).addClass(i),m.reflow(f),p.default(l).addClass(n),p.default(f).addClass(n),(r=parseInt(f.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=r):this._config.interval=this._config.defaultInterval||this._config.interval,a=m.getTransitionDurationFromElement(l),p.default(l).one(m.TRANSITION_END,function(){p.default(f).removeClass(n+" "+i).addClass(U),p.default(l).removeClass(U+" "+i+" "+n),s._isSliding=!1,setTimeout(function(){return p.default(s._element).trigger(o)},0)}).booklyEmulateTransitionEnd(a)):(p.default(l).removeClass(U),p.default(f).addClass(U),this._isSliding=!1,p.default(this._element).trigger(o)),c&&this.cycle())},r._jQueryInterface=function(i){return this.each(function(){var t=p.default(this).data(N),e=l(l({},P),p.default(this).data());"object"==typeof i&&(e=l(l({},e),i));var n="string"==typeof i?i:e.slide;if(t||(t=new r(this,e),p.default(this).data(N,t)),"number"==typeof i)t.to(i);else if("string"==typeof n){if("undefined"==typeof t[n])throw new TypeError('No method named "'+n+'"');t[n]()}else e.interval&&e.ride&&(t.pause(),t.cycle())})},r._dataApiClickHandler=function(t){var e,n,i,o=m.getSelectorFromElement(this);!o||(e=p.default(o)[0])&&p.default(e).hasClass(W)&&(n=l(l({},p.default(e).data()),p.default(this).data()),(i=this.getAttribute("data-slide-to"))&&(n.interval=!1),r._jQueryInterface.call(p.default(e),n),i&&p.default(e).data(N).to(i),t.preventDefault())},a(r,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return P}}]),r}();p.default(document).on(F.CLICK_DATA_API,tt,it._dataApiClickHandler),p.default(window).on(F.LOAD_DATA_API,function(){for(var t=[].slice.call(document.querySelectorAll(et)),e=0,n=t.length;e<n;e++){var i=p.default(t[e]);it._jQueryInterface.call(i,i.data())}}),p.default.fn[A]=it._jQueryInterface,p.default.fn[A].Constructor=it,p.default.fn[A].noConflict=function(){return p.default.fn[A]=L,it._jQueryInterface};var ot="booklyCollapse",rt="bs.collapse",at="."+rt,st=p.default.fn[ot],lt={toggle:!0,parent:""},ut={toggle:"boolean",parent:"(string|element)"},ft={SHOW:"show"+at,SHOWN:"shown"+at,HIDE:"hide"+at,HIDDEN:"hidden"+at,CLICK_DATA_API:"click"+at+".data-api"},dt="bookly-show",ct="bookly-collapse",ht="bookly-collapsing",pt="bookly-collapsed",mt="width",gt="height",_t=".bookly-show, .bookly-collapsing",vt='[data-toggle="bookly-collapse"]',yt=function(){function s(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var n=[].slice.call(document.querySelectorAll(vt)),i=0,o=n.length;i<o;i++){var r=n[i],a=m.getSelectorFromElement(r),s=[].slice.call(document.querySelectorAll(a)).filter(function(t){return t===e});null!==a&&0<s.length&&(this._selector=a,this._triggerArray.push(r))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var t=s.prototype;return t.toggle=function(){p.default(this._element).hasClass(dt)?this.hide():this.show()},t.show=function(){var t,e,n,i,o,r,a=this;this._isTransitioning||p.default(this._element).hasClass(dt)||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(_t)).filter(function(t){return"string"==typeof a._config.parent?t.getAttribute("data-parent")===a._config.parent:t.classList.contains(ct)})).length&&(t=null),t&&(e=p.default(t).not(this._selector).data(rt))&&e._isTransitioning||(n=p.default.Event(ft.SHOW),p.default(this._element).trigger(n),n.isDefaultPrevented()||(t&&(s._jQueryInterface.call(p.default(t).not(this._selector),"hide"),e||p.default(t).data(rt,null)),i=this._getDimension(),p.default(this._element).removeClass(ct).addClass(ht),this._element.style[i]=0,this._triggerArray.length&&p.default(this._triggerArray).removeClass(pt).attr("aria-expanded",!0),this.setTransitioning(!0),o="scroll"+(i[0].toUpperCase()+i.slice(1)),r=m.getTransitionDurationFromElement(this._element),p.default(this._element).one(m.TRANSITION_END,function(){p.default(a._element).removeClass(ht).addClass(ct).addClass(dt),a._element.style[i]="",a.setTransitioning(!1),p.default(a._element).trigger(ft.SHOWN)}).booklyEmulateTransitionEnd(r),this._element.style[i]=this._element[o]+"px")))},t.hide=function(){var t=this;if(!this._isTransitioning&&p.default(this._element).hasClass(dt)){var e=p.default.Event(ft.HIDE);if(p.default(this._element).trigger(e),!e.isDefaultPrevented()){var n=this._getDimension();this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",m.reflow(this._element),p.default(this._element).addClass(ht).removeClass(ct).removeClass(dt);var i=this._triggerArray.length;if(0<i)for(var o=0;o<i;o++){var r=this._triggerArray[o],a=m.getSelectorFromElement(r);null!==a&&(p.default([].slice.call(document.querySelectorAll(a))).hasClass(dt)||p.default(r).addClass(pt).attr("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[n]="";var s=m.getTransitionDurationFromElement(this._element);p.default(this._element).one(m.TRANSITION_END,function(){t.setTransitioning(!1),p.default(t._element).removeClass(ht).addClass(ct).trigger(ft.HIDDEN)}).booklyEmulateTransitionEnd(s)}}},t.setTransitioning=function(t){this._isTransitioning=t},t.dispose=function(){p.default.removeData(this._element,rt),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},t._getConfig=function(t){return(t=l(l({},lt),t)).toggle=Boolean(t.toggle),m.typeCheckConfig(ot,t,ut),t},t._getDimension=function(){return p.default(this._element).hasClass(mt)?mt:gt},t._getParent=function(){var t,n=this;m.isElement(this._config.parent)?(t=this._config.parent,"undefined"!=typeof this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var e='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',i=[].slice.call(t.querySelectorAll(e));return p.default(i).each(function(t,e){n._addAriaAndCollapsedClass(s._getTargetFromElement(e),[e])}),t},t._addAriaAndCollapsedClass=function(t,e){var n=p.default(t).hasClass(dt);e.length&&p.default(e).toggleClass(pt,!n).attr("aria-expanded",n)},s._getTargetFromElement=function(t){var e=m.getSelectorFromElement(t);return e?document.querySelector(e):null},s._jQueryInterface=function(i){return this.each(function(){var t=p.default(this),e=t.data(rt),n=l(l(l({},lt),t.data()),"object"==typeof i&&i?i:{});if(!e&&n.toggle&&/show|hide/.test(i)&&(n.toggle=!1),e||(e=new s(this,n),t.data(rt,e)),"string"==typeof i){if("undefined"==typeof e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},a(s,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return lt}}]),s}();p.default(document).on(ft.CLICK_DATA_API,vt,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var n=p.default(this),e=m.getSelectorFromElement(this),i=[].slice.call(document.querySelectorAll(e));p.default(i).each(function(){var t=p.default(this),e=t.data(rt)?"toggle":n.data();yt._jQueryInterface.call(t,e)})}),p.default.fn[ot]=yt._jQueryInterface,p.default.fn[ot].Constructor=yt,p.default.fn[ot].noConflict=function(){return p.default.fn[ot]=st,yt._jQueryInterface};var bt="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,Et=function(){for(var t=["Edge","Trident","Firefox"],e=0;e<t.length;e+=1)if(bt&&0<=navigator.userAgent.indexOf(t[e]))return 1;return 0}();var wt=bt&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then(function(){e=!1,t()}))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},Et))}};function Tt(t){return t&&"[object Function]"==={}.toString.call(t)}function Ct(t,e){if(1!==t.nodeType)return[];var n=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?n[e]:n}function St(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function Dt(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var e=Ct(t),n=e.overflow,i=e.overflowX,o=e.overflowY;return/(auto|scroll|overlay)/.test(n+o+i)?t:Dt(St(t))}function It(t){return t&&t.referenceNode?t.referenceNode:t}var At=bt&&!(!window.MSInputMethodContext||!document.documentMode),Nt=bt&&/MSIE 10/.test(navigator.userAgent);function Ot(t){return 11===t?At:10!==t&&At||Nt}function kt(t){if(!t)return document.documentElement;for(var e=Ot(10)?document.body:null,n=t.offsetParent||null;n===e&&t.nextElementSibling;)n=(t=t.nextElementSibling).offsetParent;var i=n&&n.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===Ct(n,"position")?kt(n):n:t?t.ownerDocument.documentElement:document.documentElement}function Lt(t){return null!==t.parentNode?Lt(t.parentNode):t}function Pt(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?t:e,o=n?e:t,r=document.createRange();r.setStart(i,0),r.setEnd(o,0);var a,s,l=r.commonAncestorContainer;if(t!==l&&e!==l||i.contains(o))return"BODY"===(s=(a=l).nodeName)||"HTML"!==s&&kt(a.firstElementChild)!==a?kt(l):l;var u=Lt(t);return u.host?Pt(u.host,e):Pt(t,Lt(e).host)}function xt(t,e){var n="top"===(1<arguments.length&&void 0!==e?e:"top")?"scrollTop":"scrollLeft",i=t.nodeName;if("BODY"!==i&&"HTML"!==i)return t[n];var o=t.ownerDocument.documentElement;return(t.ownerDocument.scrollingElement||o)[n]}function jt(t,e){var n="x"===e?"Left":"Top",i="Left"==n?"Right":"Bottom";return parseFloat(t["border"+n+"Width"])+parseFloat(t["border"+i+"Width"])}function Ht(t,e,n,i){return Math.max(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],Ot(10)?parseInt(n["offset"+t])+parseInt(i["margin"+("Height"===t?"Top":"Left")])+parseInt(i["margin"+("Height"===t?"Bottom":"Right")]):0)}function Rt(t){var e=t.body,n=t.documentElement,i=Ot(10)&&getComputedStyle(n);return{height:Ht("Height",e,n,i),width:Ht("Width",e,n,i)}}var Mt=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},Ft=function(t,e,n){return e&&Wt(t.prototype,e),n&&Wt(t,n),t};function Wt(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function Ut(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var qt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t};function Bt(t){return qt({},t,{right:t.left+t.width,bottom:t.top+t.height})}function Kt(t){var e,n,i={};try{Ot(10)?(i=t.getBoundingClientRect(),e=xt(t,"top"),n=xt(t,"left"),i.top+=e,i.left+=n,i.bottom+=e,i.right+=n):i=t.getBoundingClientRect()}catch(t){}var o,r={left:i.left,top:i.top,width:i.right-i.left,height:i.bottom-i.top},a="HTML"===t.nodeName?Rt(t.ownerDocument):{},s=a.width||t.clientWidth||r.width,l=a.height||t.clientHeight||r.height,u=t.offsetWidth-s,f=t.offsetHeight-l;return(u||f)&&(u-=jt(o=Ct(t),"x"),f-=jt(o,"y"),r.width-=u,r.height-=f),Bt(r)}function Qt(t,e,n){var i=2<arguments.length&&void 0!==n&&n,o=Ot(10),r="HTML"===e.nodeName,a=Kt(t),s=Kt(e),l=Dt(t),u=Ct(e),f=parseFloat(u.borderTopWidth),d=parseFloat(u.borderLeftWidth);i&&r&&(s.top=Math.max(s.top,0),s.left=Math.max(s.left,0));var c,h,p=Bt({top:a.top-s.top-f,left:a.left-s.left-d,width:a.width,height:a.height});return p.marginTop=0,p.marginLeft=0,!o&&r&&(c=parseFloat(u.marginTop),h=parseFloat(u.marginLeft),p.top-=f-c,p.bottom-=f-c,p.left-=d-h,p.right-=d-h,p.marginTop=c,p.marginLeft=h),(o&&!i?e.contains(l):e===l&&"BODY"!==l.nodeName)&&(p=function(t,e,n){var i=2<arguments.length&&void 0!==n&&n,o=xt(e,"top"),r=xt(e,"left"),a=i?-1:1;return t.top+=o*a,t.bottom+=o*a,t.left+=r*a,t.right+=r*a,t}(p,e)),p}function Vt(t){if(!t||!t.parentElement||Ot())return document.documentElement;for(var e=t.parentElement;e&&"none"===Ct(e,"transform");)e=e.parentElement;return e||document.documentElement}function Yt(t,e,n,i,o){var r,a,s,l,u,f=4<arguments.length&&void 0!==o&&o,d={top:0,left:0},c=f?Vt(t):Pt(t,It(e));"viewport"===i?d=function(t,e){var n=1<arguments.length&&void 0!==e&&e,i=t.ownerDocument.documentElement,o=Qt(t,i),r=Math.max(i.clientWidth,window.innerWidth||0),a=Math.max(i.clientHeight,window.innerHeight||0),s=n?0:xt(i),l=n?0:xt(i,"left");return Bt({top:s-o.top+o.marginTop,left:l-o.left+o.marginLeft,width:r,height:a})}(c,f):(r=void 0,"scrollParent"===i?"BODY"===(r=Dt(St(e))).nodeName&&(r=t.ownerDocument.documentElement):r="window"===i?t.ownerDocument.documentElement:i,a=Qt(r,c,f),"HTML"!==r.nodeName||function t(e){var n=e.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===Ct(e,"position"))return!0;var i=St(e);return!!i&&t(i)}(c)?d=a:(l=(s=Rt(t.ownerDocument)).height,u=s.width,d.top+=a.top-a.marginTop,d.bottom=l+a.top,d.left+=a.left-a.marginLeft,d.right=u+a.left));var h="number"==typeof(n=n||0);return d.left+=h?n:n.left||0,d.top+=h?n:n.top||0,d.right-=h?n:n.right||0,d.bottom-=h?n:n.bottom||0,d}function zt(t,e,i,n,o,r){var a=5<arguments.length&&void 0!==r?r:0;if(-1===t.indexOf("auto"))return t;var s=Yt(i,n,a,o),l={top:{width:s.width,height:e.top-s.top},right:{width:s.right-e.right,height:s.height},bottom:{width:s.width,height:s.bottom-e.bottom},left:{width:e.left-s.left,height:s.height}},u=Object.keys(l).map(function(t){return qt({key:t},l[t],{area:(e=l[t]).width*e.height});var e}).sort(function(t,e){return e.area-t.area}),f=u.filter(function(t){var e=t.width,n=t.height;return e>=i.clientWidth&&n>=i.clientHeight}),d=0<f.length?f[0].key:u[0].key,c=t.split("-")[1];return d+(c?"-"+c:"")}function Xt(t,e,n,i){var o=3<arguments.length&&void 0!==i?i:null;return Qt(n,o?Vt(e):Pt(e,It(n)),o)}function Gt(t){var e=t.ownerDocument.defaultView.getComputedStyle(t),n=parseFloat(e.marginTop||0)+parseFloat(e.marginBottom||0),i=parseFloat(e.marginLeft||0)+parseFloat(e.marginRight||0);return{width:t.offsetWidth+i,height:t.offsetHeight+n}}function $t(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function Jt(t,e,n){n=n.split("-")[0];var i=Gt(t),o={width:i.width,height:i.height},r=-1!==["right","left"].indexOf(n),a=r?"top":"left",s=r?"left":"top",l=r?"height":"width",u=r?"width":"height";return o[a]=e[a]+e[l]/2-i[l]/2,o[s]=n===s?e[s]-i[u]:e[$t(s)],o}function Zt(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function te(t,n,e){return(void 0===e?t:t.slice(0,function(t,e,n){if(Array.prototype.findIndex)return t.findIndex(function(t){return t[e]===n});var i=Zt(t,function(t){return t[e]===n});return t.indexOf(i)}(t,"name",e))).forEach(function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var e=t.function||t.fn;t.enabled&&Tt(e)&&(n.offsets.popper=Bt(n.offsets.popper),n.offsets.reference=Bt(n.offsets.reference),n=e(n,t))}),n}function ee(t,n){return t.some(function(t){var e=t.name;return t.enabled&&e===n})}function ne(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),i=0;i<e.length;i++){var o=e[i],r=o?""+o+n:t;if("undefined"!=typeof document.body.style[r])return r}return null}function ie(t){var e=t.ownerDocument;return e?e.defaultView:window}function oe(t,e,n,i){n.updateBound=i,ie(t).addEventListener("resize",n.updateBound,{passive:!0});var o=Dt(t);return function t(e,n,i,o){var r="BODY"===e.nodeName,a=r?e.ownerDocument.defaultView:e;a.addEventListener(n,i,{passive:!0}),r||t(Dt(a.parentNode),n,i,o),o.push(a)}(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function re(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,ie(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function ae(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function se(n,i){Object.keys(i).forEach(function(t){var e="";-1!==["width","height","top","right","bottom","left"].indexOf(t)&&ae(i[t])&&(e="px"),n.style[t]=i[t]+e})}function le(t,e){function n(t){return t}var i=t.offsets,o=i.popper,r=i.reference,a=Math.round,s=Math.floor,l=a(r.width),u=a(o.width),f=-1!==["left","right"].indexOf(t.placement),d=-1!==t.placement.indexOf("-"),c=e?f||d||l%2==u%2?a:s:n,h=e?a:n;return{left:c(l%2==1&&u%2==1&&!d&&e?o.left-1:o.left),top:h(o.top),bottom:h(o.bottom),right:c(o.right)}}var ue=bt&&/Firefox/i.test(navigator.userAgent);function fe(t,e,n){var i,o,r=Zt(t,function(t){return t.name===e}),a=!!r&&t.some(function(t){return t.name===n&&t.enabled&&t.order<r.order});return a||(i="`"+e+"`",o="`"+n+"`",console.warn(o+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")),a}var de=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],ce=de.slice(3);function he(t,e){var n=1<arguments.length&&void 0!==e&&e,i=ce.indexOf(t),o=ce.slice(i+1).concat(ce.slice(0,i));return n?o.reverse():o}var pe="flip",me="clockwise",ge="counterclockwise";function _e(t,o,r,e){var a=[0,0],s=-1!==["right","left"].indexOf(e),n=t.split(/(\+|\-)/).map(function(t){return t.trim()}),i=n.indexOf(Zt(n,function(t){return-1!==t.search(/,|\s/)}));n[i]&&-1===n[i].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/;return(-1!==i?[n.slice(0,i).concat([n[i].split(l)[0]]),[n[i].split(l)[1]].concat(n.slice(i+1))]:[n]).map(function(t,e){var n=(1===e?!s:s)?"height":"width",i=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,i=!0,t):i?(t[t.length-1]+=e,i=!1,t):t.concat(e)},[]).map(function(t){return function(t,e,n,i){var o=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+o[1],a=o[2];if(!r)return t;if(0!==a.indexOf("%"))return"vh"!==a&&"vw"!==a?r:("vh"===a?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*r;var s=void 0;switch(a){case"%p":s=n;break;case"%":case"%r":default:s=i}return Bt(s)[e]/100*r}(t,n,o,r)})}).forEach(function(n,i){n.forEach(function(t,e){ae(t)&&(a[i]+=t*("-"===n[e-1]?-1:1))})}),a}var ve={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e,n,i,o,r,a,s,l=t.placement,u=l.split("-")[0],f=l.split("-")[1];return f&&(n=(e=t.offsets).reference,i=e.popper,a=(o=-1!==["bottom","top"].indexOf(u))?"width":"height",s={start:Ut({},r=o?"left":"top",n[r]),end:Ut({},r,n[r]+n[a]-i[a])},t.offsets.popper=qt({},i,s[f])),t}},offset:{order:200,enabled:!0,fn:function(t,e){var n=e.offset,i=t.placement,o=t.offsets,r=o.popper,a=o.reference,s=i.split("-")[0],l=void 0,l=ae(+n)?[+n,0]:_e(n,r,a,s);return"left"===s?(r.top+=l[0],r.left-=l[1]):"right"===s?(r.top+=l[0],r.left+=l[1]):"top"===s?(r.left+=l[0],r.top-=l[1]):"bottom"===s&&(r.left+=l[0],r.top+=l[1]),t.popper=r,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,i){var e=i.boundariesElement||kt(t.instance.popper);t.instance.reference===e&&(e=kt(e));var n=ne("transform"),o=t.instance.popper.style,r=o.top,a=o.left,s=o[n];o.top="",o.left="",o[n]="";var l=Yt(t.instance.popper,t.instance.reference,i.padding,e,t.positionFixed);o.top=r,o.left=a,o[n]=s,i.boundaries=l;var u=i.priority,f=t.offsets.popper,d={primary:function(t){var e=f[t];return f[t]<l[t]&&!i.escapeWithReference&&(e=Math.max(f[t],l[t])),Ut({},t,e)},secondary:function(t){var e="right"===t?"left":"top",n=f[e];return f[t]>l[t]&&!i.escapeWithReference&&(n=Math.min(f[e],l[t]-("right"===t?f.width:f.height))),Ut({},e,n)}};return u.forEach(function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";f=qt({},f,d[e](t))}),t.offsets.popper=f,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,i=e.reference,o=t.placement.split("-")[0],r=Math.floor,a=-1!==["top","bottom"].indexOf(o),s=a?"right":"bottom",l=a?"left":"top",u=a?"width":"height";return n[s]<r(i[l])&&(t.offsets.popper[l]=r(i[l])-n[u]),n[l]>r(i[s])&&(t.offsets.popper[l]=r(i[s])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){var n;if(!fe(t.instance.modifiers,"arrow","keepTogether"))return t;var i=e.element;if("string"==typeof i){if(!(i=t.instance.popper.querySelector(i)))return t}else if(!t.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var o=t.placement.split("-")[0],r=t.offsets,a=r.popper,s=r.reference,l=-1!==["left","right"].indexOf(o),u=l?"height":"width",f=l?"Top":"Left",d=f.toLowerCase(),c=l?"left":"top",h=l?"bottom":"right",p=Gt(i)[u];s[h]-p<a[d]&&(t.offsets.popper[d]-=a[d]-(s[h]-p)),s[d]+p>a[h]&&(t.offsets.popper[d]+=s[d]+p-a[h]),t.offsets.popper=Bt(t.offsets.popper);var m=s[d]+s[u]/2-p/2,g=Ct(t.instance.popper),_=parseFloat(g["margin"+f]),v=parseFloat(g["border"+f+"Width"]),y=m-t.offsets.popper[d]-_-v,y=Math.max(Math.min(a[u]-p,y),0);return t.arrowElement=i,t.offsets.arrow=(Ut(n={},d,Math.round(y)),Ut(n,c,""),n),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(g,_){if(ee(g.instance.modifiers,"inner"))return g;if(g.flipped&&g.placement===g.originalPlacement)return g;var v=Yt(g.instance.popper,g.instance.reference,_.padding,_.boundariesElement,g.positionFixed),y=g.placement.split("-")[0],b=$t(y),E=g.placement.split("-")[1]||"",w=[];switch(_.behavior){case pe:w=[y,b];break;case me:w=he(y);break;case ge:w=he(y,!0);break;default:w=_.behavior}return w.forEach(function(t,e){if(y!==t||w.length===e+1)return g;y=g.placement.split("-")[0],b=$t(y);var n,i=g.offsets.popper,o=g.offsets.reference,r=Math.floor,a="left"===y&&r(i.right)>r(o.left)||"right"===y&&r(i.left)<r(o.right)||"top"===y&&r(i.bottom)>r(o.top)||"bottom"===y&&r(i.top)<r(o.bottom),s=r(i.left)<r(v.left),l=r(i.right)>r(v.right),u=r(i.top)<r(v.top),f=r(i.bottom)>r(v.bottom),d="left"===y&&s||"right"===y&&l||"top"===y&&u||"bottom"===y&&f,c=-1!==["top","bottom"].indexOf(y),h=!!_.flipVariations&&(c&&"start"===E&&s||c&&"end"===E&&l||!c&&"start"===E&&u||!c&&"end"===E&&f),p=!!_.flipVariationsByContent&&(c&&"start"===E&&l||c&&"end"===E&&s||!c&&"start"===E&&f||!c&&"end"===E&&u),m=h||p;(a||d||m)&&(g.flipped=!0,(a||d)&&(y=w[e+1]),m&&(E="end"===(n=E)?"start":"start"===n?"end":n),g.placement=y+(E?"-"+E:""),g.offsets.popper=qt({},g.offsets.popper,Jt(g.instance.popper,g.offsets.reference,g.placement)),g=te(g.instance.modifiers,g,"flip"))}),g},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],i=t.offsets,o=i.popper,r=i.reference,a=-1!==["left","right"].indexOf(n),s=-1===["top","left"].indexOf(n);return o[a?"left":"top"]=r[n]-(s?o[a?"width":"height"]:0),t.placement=$t(e),t.offsets.popper=Bt(o),t}},hide:{order:800,enabled:!0,fn:function(t){if(!fe(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,n=Zt(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,i=e.y,o=t.offsets.popper,r=Zt(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration;void 0!==r&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a,s,l=void 0!==r?r:e.gpuAcceleration,u=kt(t.instance.popper),f=Kt(u),d={position:o.position},c=le(t,window.devicePixelRatio<2||!ue),h="bottom"===n?"top":"bottom",p="right"===i?"left":"right",m=ne("transform"),g=void 0,_=void 0,_="bottom"==h?"HTML"===u.nodeName?-u.clientHeight+c.bottom:-f.height+c.bottom:c.top,g="right"==p?"HTML"===u.nodeName?-u.clientWidth+c.right:-f.width+c.right:c.left;l&&m?(d[m]="translate3d("+g+"px, "+_+"px, 0)",d[h]=0,d[p]=0,d.willChange="transform"):(a="bottom"==h?-1:1,s="right"==p?-1:1,d[h]=_*a,d[p]=g*s,d.willChange=h+", "+p);var v={"x-placement":t.placement};return t.attributes=qt({},v,t.attributes),t.styles=qt({},d,t.styles),t.arrowStyles=qt({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){var e,n;return se(t.instance.popper,t.styles),e=t.instance.popper,n=t.attributes,Object.keys(n).forEach(function(t){!1!==n[t]?e.setAttribute(t,n[t]):e.removeAttribute(t)}),t.arrowElement&&Object.keys(t.arrowStyles).length&&se(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,n,i,o){var r=Xt(o,e,t,n.positionFixed),a=zt(n.placement,r,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",a),se(e,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},ye=(Ft(be,[{key:"update",value:function(){return function(){var t;this.state.isDestroyed||((t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=Xt(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=zt(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=Jt(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=te(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,ee(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[ne("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=oe(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return re.call(this)}}]),be);function be(t,e){var n=this,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};Mt(this,be),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=wt(this.update.bind(this)),this.options=qt({},be.Defaults,i),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=e&&e.jquery?e[0]:e,this.options.modifiers={},Object.keys(qt({},be.Defaults.modifiers,i.modifiers)).forEach(function(t){n.options.modifiers[t]=qt({},be.Defaults.modifiers[t]||{},i.modifiers?i.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return qt({name:t},n.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(t){t.enabled&&Tt(t.onLoad)&&t.onLoad(n.reference,n.popper,n.options,t,n.state)}),this.update();var o=this.options.eventsEnabled;o&&this.enableEventListeners(),this.state.eventsEnabled=o}ye.Utils=("undefined"!=typeof window?window:global).PopperUtils,ye.placements=de,ye.Defaults=ve;var Ee="bookly-dropdown",we="bs.bookly-dropdown",Te="."+we,Ce=".data-api",Se=p.default.fn[Ee],De=new RegExp("38|40|27"),Ie={HIDE:"hide"+Te,HIDDEN:"hidden"+Te,SHOW:"show"+Te,SHOWN:"shown"+Te,CLICK:"click"+Te,CLICK_DATA_API:"click"+Te+Ce,KEYDOWN_DATA_API:"keydown"+Te+Ce,KEYUP_DATA_API:"keyup"+Te+Ce},Ae="disabled",Ne="bookly-show",Oe="bookly-dropup",ke="bookly-dropright",Le="bookly-dropleft",Pe="bookly-dropdown-menu-right",xe="position-static",je='[data-toggle="bookly-dropdown"]',He=".bookly-dropdown form",Re=".bookly-dropdown-menu",Me=".navbar-nav",Fe=".bookly-dropdown-menu .bookly-dropdown-item:not(.disabled):not(:disabled)",We="top-start",Ue="top-end",qe="bottom-start",Be="bottom-end",Ke="right-start",Qe="left-start",Ve={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},Ye={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},ze=function(){function u(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var t=u.prototype;return t.toggle=function(){var t;this._element.disabled||p.default(this._element).hasClass(Ae)||(t=p.default(this._menu).hasClass(Ne),u._clearMenus(),t||this.show(!0))},t.show=function(t){if(void 0===t&&(t=!1),!(this._element.disabled||p.default(this._element).hasClass(Ae)||p.default(this._menu).hasClass(Ne))){var e={relatedTarget:this._element},n=p.default.Event(Ie.SHOW,e),i=u._getParentFromElement(this._element);if(p.default(i).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&t){if("undefined"==typeof ye)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var o=this._element;"parent"===this._config.reference?o=i:m.isElement(this._config.reference)&&(o=this._config.reference,"undefined"!=typeof this._config.reference.jquery&&(o=this._config.reference[0])),"scrollParent"!==this._config.boundary&&p.default(i).addClass(xe),this._popper=new ye(o,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===p.default(i).closest(Me).length&&p.default(document.body).children().on("mouseover",null,p.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),p.default(this._menu).toggleClass(Ne),p.default(i).toggleClass(Ne).trigger(p.default.Event(Ie.SHOWN,e))}}},t.hide=function(){var t,e,n;this._element.disabled||p.default(this._element).hasClass(Ae)||!p.default(this._menu).hasClass(Ne)||(t={relatedTarget:this._element},e=p.default.Event(Ie.HIDE,t),n=u._getParentFromElement(this._element),p.default(n).trigger(e),e.isDefaultPrevented()||(this._popper&&this._popper.destroy(),p.default(this._menu).toggleClass(Ne),p.default(n).toggleClass(Ne).trigger(p.default.Event(Ie.HIDDEN,t))))},t.dispose=function(){p.default.removeData(this._element,we),p.default(this._element).off(Te),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var e=this;p.default(this._element).on(Ie.CLICK,function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},t._getConfig=function(t){return t=l(l(l({},this.constructor.Default),p.default(this._element).data()),t),m.typeCheckConfig(Ee,t,this.constructor.DefaultType),t},t._getMenuElement=function(){var t;return this._menu||(t=u._getParentFromElement(this._element))&&(this._menu=t.querySelector(Re)),this._menu},t._getPlacement=function(){var t=p.default(this._element.parentNode),e=qe;return t.hasClass(Oe)?(e=We,p.default(this._menu).hasClass(Pe)&&(e=Ue)):t.hasClass(ke)?e=Ke:t.hasClass(Le)?e=Qe:p.default(this._menu).hasClass(Pe)&&(e=Be),e},t._detectNavbar=function(){return 0<p.default(this._element).closest(".navbar").length},t._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=l(l({},t.offsets),e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},t._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),l(l({},t),this._config.popperConfig)},u._jQueryInterface=function(e){return this.each(function(){var t=p.default(this).data(we);if(t||(t=new u(this,"object"==typeof e?e:null),p.default(this).data(we,t)),"string"==typeof e){if("undefined"==typeof t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},u._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var e=[].slice.call(document.querySelectorAll(je)),n=0,i=e.length;n<i;n++){var o,r,a=u._getParentFromElement(e[n]),s=p.default(e[n]).data(we),l={relatedTarget:e[n]};t&&"click"===t.type&&(l.clickEvent=t),s&&(o=s._menu,p.default(a).hasClass(Ne)&&(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&p.default.contains(a,t.target)||(r=p.default.Event(Ie.HIDE,l),p.default(a).trigger(r),r.isDefaultPrevented()||("ontouchstart"in document.documentElement&&p.default(document.body).children().off("mouseover",null,p.default.noop),e[n].setAttribute("aria-expanded","false"),s._popper&&s._popper.destroy(),p.default(o).removeClass(Ne),p.default(a).removeClass(Ne).trigger(p.default.Event(Ie.HIDDEN,l))))))}},u._getParentFromElement=function(t){var e,n=m.getSelectorFromElement(t);return n&&(e=document.querySelector(n)),e||t.parentNode},u._dataApiKeydownHandler=function(t){if((/input|textarea/i.test(t.target.tagName)?!(32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||p.default(t.target).closest(Re).length)):De.test(t.which))&&(t.preventDefault(),t.stopPropagation(),!this.disabled&&!p.default(this).hasClass(Ae))){var e,n=u._getParentFromElement(this),i=p.default(n).hasClass(Ne);if(i||27!==t.which){if(!i||i&&(27===t.which||32===t.which))return 27===t.which&&(e=n.querySelector(je),p.default(e).trigger("focus")),void p.default(this).trigger("click");var o,r=[].slice.call(n.querySelectorAll(Fe)).filter(function(t){return p.default(t).is(":visible")});0!==r.length&&(o=r.indexOf(t.target),38===t.which&&0<o&&o--,40===t.which&&o<r.length-1&&o++,o<0&&(o=0),r[o].focus())}}},a(u,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return Ve}},{key:"DefaultType",get:function(){return Ye}}]),u}();p.default(document).on(Ie.KEYDOWN_DATA_API,je,ze._dataApiKeydownHandler).on(Ie.KEYDOWN_DATA_API,Re,ze._dataApiKeydownHandler).on(Ie.CLICK_DATA_API+" "+Ie.KEYUP_DATA_API,ze._clearMenus).on(Ie.CLICK_DATA_API,je,function(t){t.preventDefault(),t.stopPropagation(),ze._jQueryInterface.call(p.default(this),"toggle")}).on(Ie.CLICK_DATA_API,He,function(t){t.stopPropagation()}),p.default.fn[Ee]=ze._jQueryInterface,p.default.fn[Ee].Constructor=ze,p.default.fn[Ee].noConflict=function(){return p.default.fn[Ee]=Se,ze._jQueryInterface},"function"!=typeof NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach);var Xe="booklyModal",Ge="bs.modal",$e="."+Ge,Je=p.default.fn[Xe],Ze={backdrop:!0,keyboard:!0,focus:!0,show:!0},tn={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},en={HIDE:"hide"+$e,HIDE_PREVENTED:"hidePrevented"+$e,HIDDEN:"hidden"+$e,SHOW:"show"+$e,SHOWN:"shown"+$e,FOCUSIN:"focusin"+$e,RESIZE:"resize"+$e,CLICK_DISMISS:"click.dismiss"+$e,KEYDOWN_DISMISS:"keydown.dismiss"+$e,MOUSEUP_DISMISS:"mouseup.dismiss"+$e,MOUSEDOWN_DISMISS:"mousedown.dismiss"+$e,CLICK_DATA_API:"click"+$e+".data-api"},nn="modal-dialog-scrollable",on="modal-scrollbar-measure",rn="bookly-modal-backdrop",an="bookly-modal-open",sn="bookly-fade",ln="bookly-show",un="modal-static",fn="modal-faded",dn=".modal-dialog",cn=".modal-body",hn='[data-toggle="bookly-modal"]',pn='[data-dismiss="bookly-modal"]',mn=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",gn=".sticky-top",_n=".bookly-modal",vn=".bookly-modal.bookly-show",yn=function(){function o(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(dn),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var t=o.prototype;return t.toggle=function(t){return this._isShown?this.hide():this.show(t)},t.show=function(t){var e,n=this;this._isShown||this._isTransitioning||(p.default(this._element).hasClass(sn)&&(this._isTransitioning=!0),e=p.default.Event(en.SHOW,{relatedTarget:t}),p.default(this._element).trigger(e),this._isShown||e.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),document.querySelectorAll(_n).forEach(function(t){t.classList.add(fn)}),this._element.classList.remove(fn),p.default(this._element).on(en.CLICK_DISMISS,pn,function(t){return n.hide(t)}),p.default(this._dialog).on(en.MOUSEDOWN_DISMISS,function(){p.default(n._element).one(en.MOUSEUP_DISMISS,function(t){p.default(t.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(t)})))},t.hide=function(t){var e,n,i,o=this;t&&t.preventDefault(),this._isShown&&!this._isTransitioning&&(e=p.default.Event(en.HIDE),p.default(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,n=p.default(this._element).hasClass(sn),document.querySelectorAll(_n).forEach(function(t){t.classList.remove(fn)}),n&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),p.default(document).off(en.FOCUSIN),p.default(this._element).removeClass(ln),p.default(this._element).off(en.CLICK_DISMISS),p.default(this._dialog).off(en.MOUSEDOWN_DISMISS),n?(i=m.getTransitionDurationFromElement(this._element),p.default(this._element).one(m.TRANSITION_END,function(t){return o._hideModal(t)}).booklyEmulateTransitionEnd(i)):this._hideModal()))},t.dispose=function(){[window,this._element,this._dialog].forEach(function(t){return p.default(t).off($e)}),p.default(document).off(en.FOCUSIN),p.default.removeData(this._element,Ge),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},t.handleUpdate=function(){this._adjustDialog()},t._getConfig=function(t){return t=l(l({},Ze),t),m.typeCheckConfig(Xe,t,tn),t},t._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){var e=p.default.Event(en.HIDE_PREVENTED);if(p.default(this._element).trigger(e),e.defaultPrevented)return;this._element.classList.add(un);var n=m.getTransitionDurationFromElement(this._element);p.default(this._element).one(m.TRANSITION_END,function(){t._element.classList.remove(un)}).booklyEmulateTransitionEnd(n),this._element.focus()}else this.hide()},t._showElement=function(t){var e=this,n=p.default(this._element).hasClass(sn),i=this._dialog?this._dialog.querySelector(cn):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),p.default(this._dialog).hasClass(nn)&&i?i.scrollTop=0:this._element.scrollTop=0,n&&m.reflow(this._element),p.default(this._element).addClass(ln),this._config.focus&&this._enforceFocus();function o(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,p.default(e._element).trigger(a)}var r,a=p.default.Event(en.SHOWN,{relatedTarget:t});n?(r=m.getTransitionDurationFromElement(this._dialog),p.default(this._dialog).one(m.TRANSITION_END,o).booklyEmulateTransitionEnd(r)):o()},t._enforceFocus=function(){var e=this;p.default(document).off(en.FOCUSIN).on(en.FOCUSIN,function(t){document!==t.target&&e._element!==t.target&&0===p.default(e._element).has(t.target).length&&e._element.focus()})},t._setEscapeEvent=function(){var e=this;this._isShown&&this._config.keyboard?p.default(this._element).on(en.KEYDOWN_DISMISS,function(t){27===t.which&&e._triggerBackdropTransition()}):this._isShown||p.default(this._element).off(en.KEYDOWN_DISMISS)},t._setResizeEvent=function(){var e=this;this._isShown?p.default(window).on(en.RESIZE,function(t){return e.handleUpdate(t)}):p.default(window).off(en.RESIZE)},t._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop(function(){0===document.querySelectorAll(vn).length&&p.default(document.body).removeClass(an),t._resetAdjustments(),t._resetScrollbar(),p.default(t._element).trigger(en.HIDDEN)})},t._removeBackdrop=function(){this._backdrop&&(p.default(this._backdrop).remove(),this._backdrop=null)},t._showBackdrop=function(t){var e,n,i=this,o=p.default(this._element).hasClass(sn)?sn:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=rn,o&&this._backdrop.classList.add(o),p.default(this._backdrop).appendTo(document.body),p.default(this._element).on(en.CLICK_DISMISS,function(t){i._ignoreBackdropClick?i._ignoreBackdropClick=!1:t.target===t.currentTarget&&i._triggerBackdropTransition()}),o&&m.reflow(this._backdrop),p.default(this._backdrop).addClass(ln),!t)return;if(!o)return void t();var r=m.getTransitionDurationFromElement(this._backdrop);p.default(this._backdrop).one(m.TRANSITION_END,t).booklyEmulateTransitionEnd(r)}else{!this._isShown&&this._backdrop?(p.default(this._backdrop).removeClass(ln),e=function(){i._removeBackdrop(),t&&t()},p.default(this._element).hasClass(sn)?(n=m.getTransitionDurationFromElement(this._backdrop),p.default(this._backdrop).one(m.TRANSITION_END,e).booklyEmulateTransitionEnd(n)):e()):t&&t()}},t._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},t._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},t._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=t.left+t.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},t._setScrollbar=function(){var t,e,n,i,o=this;this._isBodyOverflowing&&(t=[].slice.call(document.querySelectorAll(mn)),e=[].slice.call(document.querySelectorAll(gn)),p.default(t).each(function(t,e){var n=e.style.paddingRight,i=p.default(e).css("padding-right");p.default(e).data("padding-right",n).css("padding-right",parseFloat(i)+o._scrollbarWidth+"px")}),p.default(e).each(function(t,e){var n=e.style.marginRight,i=p.default(e).css("margin-right");p.default(e).data("margin-right",n).css("margin-right",parseFloat(i)-o._scrollbarWidth+"px")}),n=document.body.style.paddingRight,i=p.default(document.body).css("padding-right"),p.default(document.body).data("padding-right",n).css("padding-right",parseFloat(i)+this._scrollbarWidth+"px")),p.default(document.body).addClass(an)},t._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(mn));p.default(t).each(function(t,e){var n=p.default(e).data("padding-right");p.default(e).removeData("padding-right"),e.style.paddingRight=n||""});var e=[].slice.call(document.querySelectorAll(""+gn));p.default(e).each(function(t,e){var n=p.default(e).data("margin-right");"undefined"!=typeof n&&p.default(e).css("margin-right",n).removeData("margin-right")});var n=p.default(document.body).data("padding-right");p.default(document.body).removeData("padding-right"),document.body.style.paddingRight=n||""},t._getScrollbarWidth=function(){var t=document.createElement("div");t.className=on,document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},o._jQueryInterface=function(n,i){return this.each(function(){var t=p.default(this).data(Ge),e=l(l(l({},Ze),p.default(this).data()),"object"==typeof n&&n?n:{});if(t||(t=new o(this,e),p.default(this).data(Ge,t)),"string"==typeof n){if("undefined"==typeof t[n])throw new TypeError('No method named "'+n+'"');t[n](i)}else e.show&&t.show(i)})},a(o,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return Ze}}]),o}();p.default(document).on(en.CLICK_DATA_API,hn,function(t){var e,n=this,i=m.getSelectorFromElement(this);i&&(e=document.querySelector(i));var o=p.default(e).data(Ge)?"toggle":l(l({},p.default(e).data()),p.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var r=p.default(e).one(en.SHOW,function(t){t.isDefaultPrevented()||r.one(en.HIDDEN,function(){p.default(n).is(":visible")&&n.focus()})});yn._jQueryInterface.call(p.default(e),o,this)}),p.default.fn[Xe]=yn._jQueryInterface,p.default.fn[Xe].Constructor=yn,p.default.fn[Xe].noConflict=function(){return p.default.fn[Xe]=Je,yn._jQueryInterface};var bn=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],En={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},wn=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,Tn=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Cn(t,r,e){if(0===t.length)return t;if(e&&"function"==typeof e)return e(t);for(var n=(new window.DOMParser).parseFromString(t,"text/html"),a=Object.keys(r),s=[].slice.call(n.body.querySelectorAll("*")),i=function(t){var e=s[t],n=e.nodeName.toLowerCase();if(-1===a.indexOf(e.nodeName.toLowerCase()))return e.parentNode.removeChild(e),"continue";var i=[].slice.call(e.attributes),o=[].concat(r["*"]||[],r[n]||[]);i.forEach(function(t){!function(t,e){var n=t.nodeName.toLowerCase();if(-1!==e.indexOf(n))return-1===bn.indexOf(n)||Boolean(wn.test(t.nodeValue)||Tn.test(t.nodeValue));for(var i=e.filter(function(t){return t instanceof RegExp}),o=0,r=i.length;o<r;o++)if(i[o].test(n))return 1}(t,o)&&e.removeAttribute(t.nodeName)})},o=0,l=s.length;o<l;o++)i(o);return n.body.innerHTML}var Sn="tooltip",Dn="bs.tooltip",In="."+Dn,An=p.default.fn[Sn],Nn="bs-tooltip",On=new RegExp("(^|\\s)"+Nn+"\\S+","g"),kn=["sanitize","whiteList","sanitizeFn"],Ln={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Pn={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},xn={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:En,popperConfig:null},jn="bookly-show",Hn="out",Rn={HIDE:"hide"+In,HIDDEN:"hidden"+In,SHOW:"show"+In,SHOWN:"shown"+In,INSERTED:"inserted"+In,CLICK:"click"+In,FOCUSIN:"focusin"+In,FOCUSOUT:"focusout"+In,MOUSEENTER:"mouseenter"+In,MOUSELEAVE:"mouseleave"+In},Mn="bookly-fade",Fn="bookly-show",Wn=".tooltip-inner",Un=".arrow",qn="hover",Bn="focus",Kn="click",Qn="manual",Vn=function(){function i(t,e){if("undefined"==typeof ye)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var t=i.prototype;return t.enable=function(){this._isEnabled=!0},t.disable=function(){this._isEnabled=!1},t.toggleEnabled=function(){this._isEnabled=!this._isEnabled},t.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,n=p.default(t.currentTarget).data(e);n||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),p.default(t.currentTarget).data(e,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(p.default(this.getTipElement()).hasClass(Fn))return void this._leave(null,this);this._enter(null,this)}},t.dispose=function(){clearTimeout(this._timeout),p.default.removeData(this.element,this.constructor.DATA_KEY),p.default(this.element).off(this.constructor.EVENT_KEY),p.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&p.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},t.show=function(){var e=this;if("none"===p.default(this.element).css("display"))throw new Error("Please use show on visible elements");var t=p.default.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){p.default(this.element).trigger(t);var n=m.findShadowRoot(this.element),i=p.default.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!i)return;var o=this.getTipElement(),r=m.getUID(this.constructor.NAME);o.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&p.default(o).addClass(Mn);var a="function"==typeof this.config.placement?this.config.placement.call(this,o,this.element):this.config.placement,s=this._getAttachment(a);this.addAttachmentClass(s);var l=this._getContainer();p.default(o).data(this.constructor.DATA_KEY,this),p.default.contains(this.element.ownerDocument.documentElement,this.tip)||p.default(o).appendTo(l),p.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new ye(this.element,o,this._getPopperConfig(s)),p.default(o).addClass(Fn),"ontouchstart"in document.documentElement&&p.default(document.body).children().on("mouseover",null,p.default.noop);var u,f=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,p.default(e.element).trigger(e.constructor.Event.SHOWN),t===Hn&&e._leave(null,e)};p.default(this.tip).hasClass(Mn)?(u=m.getTransitionDurationFromElement(this.tip),p.default(this.tip).one(m.TRANSITION_END,f).booklyEmulateTransitionEnd(u)):f()}},t.hide=function(t){function e(){i._hoverState!==jn&&o.parentNode&&o.parentNode.removeChild(o),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),p.default(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),t&&t()}var n,i=this,o=this.getTipElement(),r=p.default.Event(this.constructor.Event.HIDE);p.default(this.element).trigger(r),r.isDefaultPrevented()||(p.default(o).removeClass(Fn),"ontouchstart"in document.documentElement&&p.default(document.body).children().off("mouseover",null,p.default.noop),this._activeTrigger[Kn]=!1,this._activeTrigger[Bn]=!1,this._activeTrigger[qn]=!1,p.default(this.tip).hasClass(Mn)?(n=m.getTransitionDurationFromElement(o),p.default(o).one(m.TRANSITION_END,e).booklyEmulateTransitionEnd(n)):e(),this._hoverState="")},t.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},t.isWithContent=function(){return Boolean(this.getTitle())},t.addAttachmentClass=function(t){p.default(this.getTipElement()).addClass(Nn+"-"+t)},t.getTipElement=function(){return this.tip=this.tip||p.default(this.config.template)[0],this.tip},t.setContent=function(){var t=this.getTipElement();this.setElementContent(p.default(t.querySelectorAll(Wn)),this.getTitle()),p.default(t).removeClass(Mn+" "+Fn)},t.setElementContent=function(t,e){"object"!=typeof e||!e.nodeType&&!e.jquery?this.config.html?(this.config.sanitize&&(e=Cn(e,this.config.whiteList,this.config.sanitizeFn)),t.html(e)):t.text(e):this.config.html?p.default(e).parent().is(t)||t.empty().append(e):t.text(p.default(e).text())},t.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},t._getPopperConfig=function(t){var e=this;return l(l({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:Un},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}}),this.config.popperConfig)},t._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=l(l({},t.offsets),e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},t._getContainer=function(){return!1===this.config.container?document.body:m.isElement(this.config.container)?p.default(this.config.container):p.default(document).find(this.config.container)},t._getAttachment=function(t){return Pn[t.toUpperCase()]},t._setListeners=function(){var i=this;this.config.trigger.split(" ").forEach(function(t){var e,n;"click"===t?p.default(i.element).on(i.constructor.Event.CLICK,i.config.selector,function(t){return i.toggle(t)}):t!==Qn&&(e=t===qn?i.constructor.Event.MOUSEENTER:i.constructor.Event.FOCUSIN,n=t===qn?i.constructor.Event.MOUSELEAVE:i.constructor.Event.FOCUSOUT,p.default(i.element).on(e,i.config.selector,function(t){return i._enter(t)}).on(n,i.config.selector,function(t){return i._leave(t)}))}),this._hideModalHandler=function(){i.element&&i.hide()},p.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=l(l({},this.config),{},{trigger:"manual",selector:""}):this._fixTitle()},t._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},t._enter=function(t,e){var n=this.constructor.DATA_KEY;(e=e||p.default(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),p.default(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusin"===t.type?Bn:qn]=!0),p.default(e.getTipElement()).hasClass(Fn)||e._hoverState===jn?e._hoverState=jn:(clearTimeout(e._timeout),e._hoverState=jn,e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){e._hoverState===jn&&e.show()},e.config.delay.show):e.show())},t._leave=function(t,e){var n=this.constructor.DATA_KEY;(e=e||p.default(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),p.default(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusout"===t.type?Bn:qn]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState=Hn,e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){e._hoverState===Hn&&e.hide()},e.config.delay.hide):e.hide())},t._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},t._getConfig=function(t){var e=p.default(this.element).data();return Object.keys(e).forEach(function(t){-1!==kn.indexOf(t)&&delete e[t]}),"number"==typeof(t=l(l(l({},this.constructor.Default),e),"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),m.typeCheckConfig(Sn,t,this.constructor.DefaultType),t.sanitize&&(t.template=Cn(t.template,t.whiteList,t.sanitizeFn)),t},t._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},t._cleanTipClass=function(){var t=p.default(this.getTipElement()),e=t.attr("class").match(On);null!==e&&e.length&&t.removeClass(e.join(""))},t._handlePopperPlacementChange=function(t){var e=t.instance;this.tip=e.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},t._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(p.default(t).removeClass(Mn),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},i._jQueryInterface=function(n){return this.each(function(){var t=p.default(this).data(Dn),e="object"==typeof n&&n;if((t||!/dispose|hide/.test(n))&&(t||(t=new i(this,e),p.default(this).data(Dn,t)),"string"==typeof n)){if("undefined"==typeof t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},a(i,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return xn}},{key:"NAME",get:function(){return Sn}},{key:"DATA_KEY",get:function(){return Dn}},{key:"Event",get:function(){return Rn}},{key:"EVENT_KEY",get:function(){return In}},{key:"DefaultType",get:function(){return Ln}}]),i}();p.default.fn[Sn]=Vn._jQueryInterface,p.default.fn[Sn].Constructor=Vn,p.default.fn[Sn].noConflict=function(){return p.default.fn[Sn]=An,Vn._jQueryInterface};var Yn="booklyPopover",zn="bs.popover",Xn="."+zn,Gn=p.default.fn[Yn],$n="bs-popover",Jn=new RegExp("(^|\\s)"+$n+"\\S+","g"),Zn=l(l({},Vn.Default),{},{placement:"right",trigger:"click",content:"",template:'<div class="bookly-popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),ti=l(l({},Vn.DefaultType),{},{content:"(string|element|function)"}),ei="bookly-fade",ni="bookly-show",ii=".popover-header",oi=".popover-body",ri={HIDE:"hide"+Xn,HIDDEN:"hidden"+Xn,SHOW:"show"+Xn,SHOWN:"shown"+Xn,INSERTED:"inserted"+Xn,CLICK:"click"+Xn,FOCUSIN:"focusin"+Xn,FOCUSOUT:"focusout"+Xn,MOUSEENTER:"mouseenter"+Xn,MOUSELEAVE:"mouseleave"+Xn},ai=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),(e.prototype.constructor=e).__proto__=n;var o=i.prototype;return o.isWithContent=function(){return this.getTitle()||this._getContent()},o.addAttachmentClass=function(t){p.default(this.getTipElement()).addClass($n+"-"+t)},o.getTipElement=function(){return this.tip=this.tip||p.default(this.config.template)[0],this.tip},o.setContent=function(){var t=p.default(this.getTipElement());this.setElementContent(t.find(ii),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(oi),e),t.removeClass(ei+" "+ni)},o._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},o._cleanTipClass=function(){var t=p.default(this.getTipElement()),e=t.attr("class").match(Jn);null!==e&&0<e.length&&t.removeClass(e.join(""))},i._jQueryInterface=function(n){return this.each(function(){var t=p.default(this).data(zn),e="object"==typeof n?n:null;if((t||!/dispose|hide/.test(n))&&(t||(t=new i(this,e),p.default(this).data(zn,t)),"string"==typeof n)){if("undefined"==typeof t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},a(i,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return Zn}},{key:"NAME",get:function(){return Yn}},{key:"DATA_KEY",get:function(){return zn}},{key:"Event",get:function(){return ri}},{key:"EVENT_KEY",get:function(){return Xn}},{key:"DefaultType",get:function(){return ti}}]),i}(Vn);p.default.fn[Yn]=ai._jQueryInterface,p.default.fn[Yn].Constructor=ai,p.default.fn[Yn].noConflict=function(){return p.default.fn[Yn]=Gn,ai._jQueryInterface};var si="scrollspy",li="bs.scrollspy",ui="."+li,fi=p.default.fn[si],di={offset:10,method:"auto",target:""},ci={offset:"number",method:"string",target:"(string|element)"},hi={ACTIVATE:"activate"+ui,SCROLL:"scroll"+ui,LOAD_DATA_API:"load"+ui+".data-api"},pi="dropdown-item",mi="active",gi='[data-spy="scroll"]',_i=".nav, .list-group",vi=".nav-link",yi=".nav-item",bi=".list-group-item",Ei=".dropdown",wi=".dropdown-item",Ti=".dropdown-toggle",Ci="offset",Si="position",Di=function(){function n(t,e){var n=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+vi+","+this._config.target+" "+bi+","+this._config.target+" "+wi,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,p.default(this._scrollElement).on(hi.SCROLL,function(t){return n._process(t)}),this.refresh(),this._process()}var t=n.prototype;return t.refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?Ci:Si,o="auto"===this._config.method?t:this._config.method,r=o===Si?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(t){var e,n=m.getSelectorFromElement(t);if(n&&(e=document.querySelector(n)),e){var i=e.getBoundingClientRect();if(i.width||i.height)return[p.default(e)[o]().top+r,n]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},t.dispose=function(){p.default.removeData(this._element,li),p.default(this._scrollElement).off(ui),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},t._getConfig=function(t){var e;return"string"!=typeof(t=l(l({},di),"object"==typeof t&&t?t:{})).target&&((e=p.default(t.target).attr("id"))||(e=m.getUID(si),p.default(t.target).attr("id",e)),t.target="#"+e),m.typeCheckConfig(si,t,ci),t},t._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},t._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},t._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},t._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),n<=t){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var o=this._offsets.length;o--;){this._activeTarget!==this._targets[o]&&t>=this._offsets[o]&&("undefined"==typeof this._offsets[o+1]||t<this._offsets[o+1])&&this._activate(this._targets[o])}}},t._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),n=p.default([].slice.call(document.querySelectorAll(t.join(","))));n.hasClass(pi)?(n.closest(Ei).find(Ti).addClass(mi),n.addClass(mi)):(n.addClass(mi),n.parents(_i).prev(vi+", "+bi).addClass(mi),n.parents(_i).prev(yi).children(vi).addClass(mi)),p.default(this._scrollElement).trigger(hi.ACTIVATE,{relatedTarget:e})},t._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(t){return t.classList.contains(mi)}).forEach(function(t){return t.classList.remove(mi)})},n._jQueryInterface=function(e){return this.each(function(){var t=p.default(this).data(li);if(t||(t=new n(this,"object"==typeof e&&e),p.default(this).data(li,t)),"string"==typeof e){if("undefined"==typeof t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},a(n,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"Default",get:function(){return di}}]),n}();p.default(window).on(hi.LOAD_DATA_API,function(){for(var t=[].slice.call(document.querySelectorAll(gi)),e=t.length;e--;){var n=p.default(t[e]);Di._jQueryInterface.call(n,n.data())}}),p.default.fn[si]=Di._jQueryInterface,p.default.fn[si].Constructor=Di,p.default.fn[si].noConflict=function(){return p.default.fn[si]=fi,Di._jQueryInterface};var Ii="booklyTab",Ai="bs.tab",Ni="."+Ai,Oi=p.default.fn[Ii],ki={HIDE:"hide"+Ni,HIDDEN:"hidden"+Ni,SHOW:"show"+Ni,SHOWN:"shown"+Ni,CLICK_DATA_API:"click"+Ni+".data-api"},Li="dropdown-menu",Pi="active",xi="disabled",ji="bookly-fade",Hi="bookly-show",Ri=".dropdown",Mi=".nav, .list-group",Fi=".active",Wi="> li > .active",Ui='[data-toggle="bookly-tab"], [data-toggle="bookly-pill"], [data-toggle="bookly-list"]',qi=".dropdown-toggle",Bi="> .dropdown-menu .active",Ki=function(){function i(t){this._element=t}var t=i.prototype;return t.show=function(){var t,e,n,i,o,r,a,s,l=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&p.default(this._element).hasClass(Pi)||p.default(this._element).hasClass(xi)||(e=p.default(this._element).closest(Mi)[0],n=m.getSelectorFromElement(this._element),e&&(i="UL"===e.nodeName||"OL"===e.nodeName?Wi:Fi,o=(o=p.default.makeArray(p.default(e).find(i)))[o.length-1]),r=p.default.Event(ki.HIDE,{relatedTarget:this._element}),a=p.default.Event(ki.SHOW,{relatedTarget:o}),o&&p.default(o).trigger(r),p.default(this._element).trigger(a),a.isDefaultPrevented()||r.isDefaultPrevented()||(n&&(t=document.querySelector(n)),this._activate(this._element,e),s=function(){var t=p.default.Event(ki.HIDDEN,{relatedTarget:l._element}),e=p.default.Event(ki.SHOWN,{relatedTarget:o});p.default(o).trigger(t),p.default(l._element).trigger(e)},t?this._activate(t,t.parentNode,s):s()))},t.dispose=function(){p.default.removeData(this._element,Ai),this._element=null},t._activate=function(t,e,n){function i(){return r._transitionComplete(t,a,n)}var o,r=this,a=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?p.default(e).children(Fi):p.default(e).find(Wi))[0],s=n&&a&&p.default(a).hasClass(ji);a&&s?(o=m.getTransitionDurationFromElement(a),p.default(a).removeClass(Hi).one(m.TRANSITION_END,i).booklyEmulateTransitionEnd(o)):i()},t._transitionComplete=function(t,e,n){var i,o,r;e&&(p.default(e).removeClass(Pi),(i=p.default(e.parentNode).find(Bi)[0])&&p.default(i).removeClass(Pi),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)),p.default(t).addClass(Pi),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),m.reflow(t),t.classList.contains(ji)&&t.classList.add(Hi),t.parentNode&&p.default(t.parentNode).hasClass(Li)&&((o=p.default(t).closest(Ri)[0])&&(r=[].slice.call(o.querySelectorAll(qi)),p.default(r).addClass(Pi)),t.setAttribute("aria-expanded",!0)),n&&n()},i._jQueryInterface=function(n){return this.each(function(){var t=p.default(this),e=t.data(Ai);if(e||(e=new i(this),t.data(Ai,e)),"string"==typeof n){if("undefined"==typeof e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},a(i,null,[{key:"VERSION",get:function(){return"4.4.1"}}]),i}();p.default(document).on(ki.CLICK_DATA_API,Ui,function(t){t.preventDefault(),Ki._jQueryInterface.call(p.default(this),"show")}),p.default.fn[Ii]=Ki._jQueryInterface,p.default.fn[Ii].Constructor=Ki,p.default.fn[Ii].noConflict=function(){return p.default.fn[Ii]=Oi,Ki._jQueryInterface};var Qi="toast",Vi="bs.toast",Yi="."+Vi,zi=p.default.fn[Qi],Xi={CLICK_DISMISS:"click.dismiss"+Yi,HIDE:"hide"+Yi,HIDDEN:"hidden"+Yi,SHOW:"show"+Yi,SHOWN:"shown"+Yi},Gi="fade",$i="hide",Ji="bookly-show",Zi="showing",to={animation:"boolean",autohide:"boolean",delay:"number"},eo={animation:!0,autohide:!0,delay:500},no='[data-dismiss="toast"]',io=function(){function i(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}var t=i.prototype;return t.show=function(){var t,e,n=this,i=p.default.Event(Xi.SHOW);p.default(this._element).trigger(i),i.isDefaultPrevented()||(this._config.animation&&this._element.classList.add(Gi),t=function(){n._element.classList.remove(Zi),n._element.classList.add(Ji),p.default(n._element).trigger(Xi.SHOWN),n._config.autohide&&(n._timeout=setTimeout(function(){n.hide()},n._config.delay))},this._element.classList.remove($i),m.reflow(this._element),this._element.classList.add(Zi),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),p.default(this._element).one(m.TRANSITION_END,t).booklyEmulateTransitionEnd(e)):t())},t.hide=function(){var t;this._element.classList.contains(Ji)&&(t=p.default.Event(Xi.HIDE),p.default(this._element).trigger(t),t.isDefaultPrevented()||this._close())},t.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains(Ji)&&this._element.classList.remove(Ji),p.default(this._element).off(Xi.CLICK_DISMISS),p.default.removeData(this._element,Vi),this._element=null,this._config=null},t._getConfig=function(t){return t=l(l(l({},eo),p.default(this._element).data()),"object"==typeof t&&t?t:{}),m.typeCheckConfig(Qi,t,this.constructor.DefaultType),t},t._setListeners=function(){var t=this;p.default(this._element).on(Xi.CLICK_DISMISS,no,function(){return t.hide()})},t._close=function(){function t(){n._element.classList.add($i),p.default(n._element).trigger(Xi.HIDDEN)}var e,n=this;this._element.classList.remove(Ji),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),p.default(this._element).one(m.TRANSITION_END,t).booklyEmulateTransitionEnd(e)):t()},i._jQueryInterface=function(n){return this.each(function(){var t=p.default(this),e=t.data(Vi);if(e||(e=new i(this,"object"==typeof n&&n),t.data(Vi,e)),"string"==typeof n){if("undefined"==typeof e[n])throw new TypeError('No method named "'+n+'"');e[n](this)}})},a(i,null,[{key:"VERSION",get:function(){return"4.4.1"}},{key:"DefaultType",get:function(){return to}},{key:"Default",get:function(){return eo}}]),i}();p.default.fn[Qi]=io._jQueryInterface,p.default.fn[Qi].Constructor=io,p.default.fn[Qi].noConflict=function(){return p.default.fn[Qi]=zi,io._jQueryInterface},t.Alert=y,t.Button=I,t.Carousel=it,t.Collapse=yt,t.Dropdown=ze,t.Modal=yn,t.Popover=ai,t.Scrollspy=Di,t.Tab=Ki,t.Toast=io,t.Tooltip=Vn,t.Util=m,Object.defineProperty(t,"__esModule",{value:!0})});
