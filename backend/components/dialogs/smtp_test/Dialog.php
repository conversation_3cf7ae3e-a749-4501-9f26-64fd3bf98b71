<?php
namespace Bookly\Backend\Components\Dialogs\SmtpTest;

use Bookly\Lib;

class Dialog extends Lib\Base\Component
{
    /**
     * <PERSON><PERSON> create service dialog.
     */
    public static function render()
    {
        self::enqueueScripts( array(
            'module' => array( 'js/smtp-test-dialog.js' => array( 'bookly-backend-globals' ) ),
        ) );

        wp_localize_script( 'bookly-smtp-test-dialog.js', 'BooklySmtpTestDialogL10n', array(
            'success' => __( 'Success', 'bookly' ),
            'failed' => __( 'Failed', 'bookly' ),
        ) );

        self::renderTemplate( 'dialog' );
    }
}