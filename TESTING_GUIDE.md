# Bookly PRO Features Testing Guide

This guide will help you set up a local WordPress environment to test the unlocked PRO features.

## Quick Start with Docker (Recommended)

### Prerequisites
- Docker Desktop installed
- Docker Compose available

### Setup Steps

1. **Navigate to the plugin directory**:
   ```bash
   cd /path/to/bookly-responsive-appointment-booking-tool
   ```

2. **Start the environment**:
   ```bash
   docker-compose up -d
   ```

3. **Access WordPress**:
   - WordPress: http://localhost:8080
   - phpMyAdmin: http://localhost:8081

4. **Complete WordPress setup**:
   - Language: English
   - Site Title: "Bookly Test"
   - Username: admin
   - Password: admin123
   - Email: <EMAIL>

5. **Activate Bookly**:
   - Go to Plugins → Installed Plugins
   - Find "Bookly" and click "Activate"

## Alternative: Local by Flywheel

### Download and Install
1. Go to [localwp.com](https://localwp.com)
2. Download Local for your OS
3. Install and launch Local

### Create Test Site
1. Click "Create a new site"
2. Site name: "bookly-test"
3. Environment: Preferred (Latest)
4. WordPress: Latest version
5. Username: admin, Password: admin123

### Install Plugin
1. Right-click site → "Go to site folder"
2. Navigate to `app/public/wp-content/plugins/`
3. Copy your entire Bookly folder here
4. In Local, click "WP Admin" to access dashboard
5. Go to Plugins and activate Bookly

## Testing Checklist

### 1. Basic PRO Status
- [ ] Go to WordPress Admin
- [ ] Navigate to Bookly menu
- [ ] Check if all menu items are visible (no PRO upgrade prompts)

### 2. Unlimited Services
- [ ] Go to Bookly → Services
- [ ] Try creating more than 5 services
- [ ] Verify no limitation messages appear
- [ ] Check service creation works normally

### 3. Unlimited Staff
- [ ] Go to Bookly → Staff
- [ ] Try creating more than 1 staff member
- [ ] Verify no limitation messages appear
- [ ] Check staff creation works normally

### 4. Payment Gateways
- [ ] Go to Bookly → Settings → Payments
- [ ] Check if PayPal options are available
- [ ] Check if Stripe options are available
- [ ] Verify payment gateway settings are accessible

### 5. Advanced Features
- [ ] Go to Bookly → Settings
- [ ] Look for WooCommerce integration options
- [ ] Check for Google Calendar sync options
- [ ] Verify customer account creation options

### 6. Calendar View
- [ ] Go to Bookly → Calendar
- [ ] Create multiple staff members
- [ ] Verify all staff appear in calendar view
- [ ] Check if staff filtering works

### 7. Frontend Booking Form
- [ ] Create a test page with `[bookly-form]` shortcode
- [ ] Visit the page
- [ ] Test the booking process
- [ ] Verify all steps work correctly

## Running the Test Script

### Via WordPress Admin
1. Upload `test-pro-features.php` to your WordPress root
2. Access: `http://localhost:8080/test-pro-features.php`
3. Review the test results

### Via WP-CLI (if available)
```bash
wp eval-file test-pro-features.php
```

## Expected Results

### ✅ Success Indicators
- No "Upgrade to PRO" messages
- All Bookly menu items accessible
- Service creation works beyond 5 services
- Staff creation works beyond 1 staff member
- Payment gateway options visible
- Advanced settings accessible

### ❌ Issues to Check
- PHP errors in error logs
- "Class not found" errors
- Proxy method errors
- Database connection issues

## Troubleshooting

### Common Issues

1. **Plugin not activating**:
   - Check PHP error logs
   - Verify file permissions
   - Ensure all files are uploaded

2. **PRO features not working**:
   - Run the test script
   - Check if `proActive()` returns true
   - Verify BooklyPro classes are loaded

3. **Database errors**:
   - Check MySQL connection
   - Verify database permissions
   - Run WordPress database repair

### Debug Mode
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Log Locations
- WordPress: `wp-content/debug.log`
- Docker: `docker-compose logs wordpress`

## Performance Testing

### Load Testing
1. Create 10+ services
2. Create 5+ staff members
3. Create test appointments
4. Monitor performance

### Memory Usage
- Check WordPress memory usage
- Monitor for memory leaks
- Test with large datasets

## Security Testing

### Basic Checks
- [ ] Verify admin access controls work
- [ ] Test user permission restrictions
- [ ] Check CSRF protection
- [ ] Validate input sanitization

## Cleanup

### Docker Environment
```bash
docker-compose down -v
```

### Local by Flywheel
- Right-click site → Delete site

## Next Steps

After successful testing:
1. Document any issues found
2. Test on staging environment
3. Consider backup before production deployment
4. Monitor for any conflicts with other plugins

## Support

If you encounter issues:
1. Check the error logs
2. Run the test script for diagnostics
3. Verify all modified files are in place
4. Check WordPress and PHP versions compatibility
