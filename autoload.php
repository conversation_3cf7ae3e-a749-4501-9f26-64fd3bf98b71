<?php defined( 'ABSPATH' ) || exit; // Exit if accessed directly

/**
 * Bookly autoload.
 * @param $class
 */
function bookly_loader( $class )
{
    // Handle both Bookly and BooklyPro namespaces
    if ( preg_match( '/^(Bookly(?:Pro)?)\\\\(.+)?([^\\\\]+)$/U', ltrim( $class, '\\' ), $match ) ) {
        $namespace = $match[1];
        $path = $match[2];
        $classname = $match[3];

        // For BooklyPro, map to lib/BooklyPro directory
        if ( $namespace === 'BooklyPro' ) {
            $file = __DIR__ . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'BooklyPro' . DIRECTORY_SEPARATOR
                    . strtolower( str_replace( '\\', DIRECTORY_SEPARATOR, preg_replace( '/([a-z])([A-Z])/', '$1_$2', $path ) ) )
                    . $classname
                    . '.php';
        } else {
            // Original Bookly namespace handling
            $file = __DIR__ . DIRECTORY_SEPARATOR
                    . strtolower( str_replace( '\\', DIRECTORY_SEPARATOR, preg_replace( '/([a-z])([A-Z])/', '$1_$2', $path ) ) )
                    . $classname
                    . '.php';
        }

        if ( is_readable( $file ) ) {
            require_once $file;
        }
    }
}
spl_autoload_register( 'bookly_loader', true, true );