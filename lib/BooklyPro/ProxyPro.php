<?php
namespace BooklyPro\Lib;

/**
 * Mock ProxyPro class to handle PRO method invocations
 */
class ProxyPro
{
    private static $registered_proxies = array();
    private static $method_implementations = array();

    /**
     * Initialize proxy for a class
     *
     * @param string $proxy_class
     * @param \ReflectionClass $reflection
     */
    public static function init( $proxy_class, $reflection )
    {
        self::$registered_proxies[ $proxy_class ] = $reflection;
        
        // Parse @method annotations to understand available methods
        $doc_comment = $reflection->getDocComment();
        if ( $doc_comment ) {
            preg_match_all( '/@method\s+static\s+(\w+|\w+\[\]|\\\\\w+(?:\\\\\w+)*(?:\[\])?)\s+(\w+)\s*\([^)]*\)/', $doc_comment, $matches );
            if ( ! empty( $matches[2] ) ) {
                foreach ( $matches[2] as $method_name ) {
                    self::$method_implementations[ $proxy_class ][ $method_name ] = true;
                }
            }
        }
    }

    /**
     * Check if method can be invoked
     *
     * @param string $proxy_class
     * @param string $method
     * @return bool
     */
    public static function canInvoke( $proxy_class, $method )
    {
        return isset( self::$method_implementations[ $proxy_class ][ $method ] );
    }

    /**
     * Invoke proxy method
     *
     * @param string $proxy_class
     * @param string $method
     * @param array $args
     * @return mixed
     */
    public static function invoke( $proxy_class, $method, $args )
    {
        // Handle specific important methods
        switch ( $proxy_class ) {
            case 'Bookly\Lib\Proxy\Pro':
                return self::handleLibProxyPro( $method, $args );
            case 'Bookly\Backend\Components\Dialogs\Staff\Edit\Proxy\Pro':
                return self::handleStaffEditPro( $method, $args );
            case 'Bookly\Backend\Modules\Settings\Proxy\Pro':
                return self::handleSettingsPro( $method, $args );
            case 'Bookly\Frontend\Modules\Booking\Proxy\Pro':
                return self::handleBookingPro( $method, $args );
            default:
                // Default behavior for unhandled proxies
                return self::handleDefault( $method, $args );
        }
    }

    /**
     * Handle Lib\Proxy\Pro methods
     */
    private static function handleLibProxyPro( $method, $args )
    {
        switch ( $method ) {
            case 'graceExpired':
                return false; // Never expired
            case 'addLicenseBooklyMenuItem':
                return; // Do nothing
            case 'getMinimumTimePriorBooking':
                return isset( $args[0] ) ? 0 : null; // No minimum time restriction
            case 'getWorkingTimeLimitError':
                return false; // No working time limit errors
            case 'createWPUser':
                return ''; // Return empty string for user creation
            case 'createBackendPayment':
                return; // Do nothing for payment creation
            case 'deleteGoogleCalendarEvent':
            case 'deleteOnlineMeeting':
            case 'syncGoogleCalendarEvent':
            case 'revokeGoogleCalendarToken':
                return; // Do nothing for calendar operations
            case 'showFacebookLoginButton':
                return false; // Don't show Facebook login
            case 'getFullAddressByCustomerData':
                return isset( $args[0] ) && is_array( $args[0] ) ? '' : '';
            default:
                return self::handleDefault( $method, $args );
        }
    }

    /**
     * Handle Staff Edit Pro methods
     */
    private static function handleStaffEditPro( $method, $args )
    {
        switch ( $method ) {
            case 'enqueueAssets':
                return; // Do nothing
            case 'renderArchivingComponents':
            case 'renderCreateWPUser':
                echo ''; // Render empty content
                return;
            case 'getAdvancedHtml':
                return ''; // Return empty HTML
            case 'renderGoogleCalendarsList':
                return ''; // Return empty calendar list
            default:
                return self::handleDefault( $method, $args );
        }
    }

    /**
     * Handle Settings Pro methods
     */
    private static function handleSettingsPro( $method, $args )
    {
        switch ( $method ) {
            case 'renderMinimumTimeRequirement':
            case 'renderNewClientAccountRole':
            case 'renderNewStaffAccountRole':
            case 'renderMenuItem':
                echo ''; // Render empty content
                return;
            default:
                return self::handleDefault( $method, $args );
        }
    }

    /**
     * Handle Booking Pro methods
     */
    private static function handleBookingPro( $method, $args )
    {
        switch ( $method ) {
            case 'getHtmlPaymentImpossible':
                return '';
            case 'filterGateways':
                return isset( $args[0] ) ? $args[0] : array();
            case 'findOneGiftCardByCode':
                return null;
            case 'getCustomerByFacebookId':
                return false;
            default:
                return self::handleDefault( $method, $args );
        }
    }

    /**
     * Default handler for unspecified methods
     */
    private static function handleDefault( $method, $args )
    {
        // Return appropriate default based on method name pattern
        if ( preg_match( '/^render/', $method ) ) {
            echo ''; // Render methods output empty content
            return;
        } elseif ( preg_match( '/^get|^find/', $method ) ) {
            return null; // Getter/finder methods return null
        } elseif ( preg_match( '/^is|^has|^can/', $method ) ) {
            return false; // Boolean methods return false
        } elseif ( ! empty( $args ) ) {
            return $args[0]; // Return first argument if available
        }
        
        return null;
    }
}
