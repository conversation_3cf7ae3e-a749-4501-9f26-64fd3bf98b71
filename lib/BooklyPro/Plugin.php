<?php
namespace BooklyPro\Lib;

use Bookly\Lib;

/**
 * Mock BooklyPro Plugin class to enable PRO features
 * This class simulates the presence of BooklyPro plugin
 */
class Plugin extends Lib\Base\Plugin
{
    protected static $prefix = 'bookly_pro_';
    protected static $title = 'Bookly Pro (Mock)';
    protected static $version = '25.0';
    protected static $slug = 'bookly-addon-pro';
    protected static $directory;
    protected static $main_file;
    protected static $basename;
    protected static $text_domain = 'bookly-addon-pro';
    protected static $root_namespace = 'BooklyPro';
    protected static $embedded = false;

    /**
     * @inheritDoc
     */
    protected static function init()
    {
        // Initialize PRO features
        self::initProxyMethods();
    }

    /**
     * Initialize proxy methods for PRO features
     */
    private static function initProxyMethods()
    {
        // Create global aliases for PRO classes
        if ( ! class_exists( 'ProxyPro', false ) ) {
            class_alias( '\BooklyPro\Lib\ProxyPro', 'ProxyPro' );
        }
        if ( ! class_exists( 'PluginPro', false ) ) {
            class_alias( '\BooklyPro\Lib\Plugin', 'PluginPro' );
        }
    }

    /**
     * Register hooks for PRO features
     */
    public static function registerHooks( $plugin_class )
    {
        // Handle PRO-specific hooks
    }

    /**
     * Initialize plugin update checker
     */
    public static function initPluginUpdateChecker( $plugin_class )
    {
        // Mock update checker - do nothing
    }

    /**
     * @inheritDoc
     */
    protected static function registerAjax()
    {
        // Register PRO AJAX handlers
    }

    /**
     * @inheritDoc
     */
    public static function run()
    {
        // Load text domain
        load_plugin_textdomain( 'bookly-addon-pro', false, self::getSlug() . '/languages' );

        // Enable PRO payment options by default
        self::enableProPaymentOptions();

        parent::run();
    }

    /**
     * Enable PRO payment options
     */
    private static function enableProPaymentOptions()
    {
        // Enable PayPal if not set
        if ( get_option( 'bookly_paypal_enabled' ) === false ) {
            update_option( 'bookly_paypal_enabled', '1' );
        }

        // Enable local payments if not set
        if ( get_option( 'bookly_pmt_local' ) === false ) {
            update_option( 'bookly_pmt_local', '1' );
        }

        // Enable Stripe Cloud if not set
        if ( get_option( 'bookly_cloud_stripe_enabled' ) === false ) {
            update_option( 'bookly_cloud_stripe_enabled', '1' );
        }

        // Enable WooCommerce integration if not set
        if ( get_option( 'bookly_wc_enabled' ) === false ) {
            update_option( 'bookly_wc_enabled', '1' );
        }

        // Enable advanced booking features
        if ( get_option( 'bookly_app_show_login_button' ) === false ) {
            update_option( 'bookly_app_show_login_button', '1' );
        }

        // Enable customer account creation
        if ( get_option( 'bookly_cst_create_account' ) === false ) {
            update_option( 'bookly_cst_create_account', '1' );
        }
    }

    /**
     * @inheritDoc
     */
    public static function getSlug()
    {
        return self::$slug;
    }

    /**
     * @inheritDoc
     */
    public static function getTitle()
    {
        return self::$title;
    }

    /**
     * @inheritDoc
     */
    public static function getVersion()
    {
        return self::$version;
    }

    /**
     * @inheritDoc
     */
    public static function getTextDomain()
    {
        return self::$text_domain;
    }

    /**
     * @inheritDoc
     */
    public static function getRootNamespace()
    {
        return self::$root_namespace;
    }

    /**
     * @inheritDoc
     */
    public static function getDirectory()
    {
        if ( self::$directory === null ) {
            self::$directory = dirname( dirname( __DIR__ ) );
        }
        return self::$directory;
    }

    /**
     * @inheritDoc
     */
    public static function getMainFile()
    {
        if ( self::$main_file === null ) {
            self::$main_file = self::getDirectory() . '/main.php';
        }
        return self::$main_file;
    }

    /**
     * @inheritDoc
     */
    public static function getBasename()
    {
        if ( self::$basename === null ) {
            self::$basename = plugin_basename( self::getMainFile() );
        }
        return self::$basename;
    }

    /**
     * @inheritDoc
     */
    public static function embedded()
    {
        return self::$embedded;
    }

    /**
     * Mock purchase code for license validation
     */
    public static function getPurchaseCode()
    {
        return 'MOCK-PRO-LICENSE-12345';
    }

    /**
     * Always return false for grace period expiration
     */
    public static function graceExpired()
    {
        return false;
    }
}
