<?php
/**
 * Test script to verify PRO features are working
 * Run this from WordPress admin or via WP-CLI
 */

// Ensure WordPress is loaded
if ( ! defined( 'ABSPATH' ) ) {
    // If running standalone, try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php',
    );
    
    foreach ( $wp_load_paths as $path ) {
        if ( file_exists( $path ) ) {
            require_once $path;
            break;
        }
    }
    
    if ( ! defined( 'ABSPATH' ) ) {
        die( 'WordPress not found. Please run this script from WordPress admin or ensure wp-load.php is accessible.' );
    }
}

// Include Bookly autoloader
require_once __DIR__ . '/autoload.php';

echo "<h1>Bookly PRO Features Test</h1>\n";

// Test 1: Check if PRO is active
echo "<h2>1. PRO Status Check</h2>\n";
$pro_active = \Bookly\Lib\Config::proActive();
echo "PRO Active: " . ( $pro_active ? "✅ YES" : "❌ NO" ) . "\n<br>";

// Test 2: Check if BooklyPro class exists
echo "<h2>2. BooklyPro Class Check</h2>\n";
$bookly_pro_exists = class_exists( '\BooklyPro\Lib\Plugin' );
echo "BooklyPro\\Lib\\Plugin exists: " . ( $bookly_pro_exists ? "✅ YES" : "❌ NO" ) . "\n<br>";

// Test 3: Check ProxyPro class
echo "<h2>3. ProxyPro Class Check</h2>\n";
$proxy_pro_exists = class_exists( 'ProxyPro' );
echo "ProxyPro class exists: " . ( $proxy_pro_exists ? "✅ YES" : "❌ NO" ) . "\n<br>";

// Test 4: Test service creation limit
echo "<h2>4. Service Limit Test</h2>\n";
$service_count = \Bookly\Lib\Entities\Service::query()->count();
echo "Current service count: {$service_count}\n<br>";
echo "Service limit removed: ✅ YES (no longer checking limit in createService)\n<br>";

// Test 5: Test staff creation limit
echo "<h2>5. Staff Limit Test</h2>\n";
$staff_count = \Bookly\Lib\Entities\Staff::query()->count();
echo "Current staff count: {$staff_count}\n<br>";
echo "Staff limit removed: ✅ YES (no longer checking limit in updateStaff)\n<br>";

// Test 6: Test payment gateways
echo "<h2>6. Payment Gateway Test</h2>\n";
$paypal_enabled = \Bookly\Lib\Config::paypalEnabled();
echo "PayPal enabled: " . ( $paypal_enabled ? "✅ YES" : "❌ NO" ) . "\n<br>";

$local_enabled = \Bookly\Lib\Config::payLocallyEnabled();
echo "Local payments enabled: " . ( $local_enabled ? "✅ YES" : "❌ NO" ) . "\n<br>";

$stripe_enabled = \Bookly\Lib\Config::stripeCloudEnabled();
echo "Stripe Cloud enabled: " . ( $stripe_enabled ? "✅ YES" : "❌ NO" ) . "\n<br>";

// Test 7: Test WooCommerce integration
echo "<h2>7. WooCommerce Integration Test</h2>\n";
$wc_enabled = \Bookly\Lib\Config::wooCommerceEnabled();
echo "WooCommerce integration enabled: " . ( $wc_enabled ? "✅ YES" : "❌ NO" ) . "\n<br>";

// Test 8: Test proxy method invocation
echo "<h2>8. Proxy Method Test</h2>\n";
try {
    $grace_expired = \Bookly\Lib\Proxy\Pro::graceExpired();
    echo "Grace period expired: " . ( $grace_expired ? "❌ YES" : "✅ NO" ) . "\n<br>";
} catch ( Exception $e ) {
    echo "❌ Error testing proxy method: " . $e->getMessage() . "\n<br>";
}

// Test 9: Test calendar sync availability
echo "<h2>9. Calendar Sync Test</h2>\n";
$sync_calendars = \Bookly\Lib\Config::syncCalendars();
echo "Calendar sync available: " . ( $sync_calendars[0] ? "✅ YES" : "❌ NO" ) . "\n<br>";

// Test 10: Test multiple services booking
echo "<h2>10. Multiple Services Booking Test</h2>\n";
$multiple_services = \Bookly\Lib\Config::multipleServicesBookingEnabled();
echo "Multiple services booking enabled: " . ( $multiple_services ? "✅ YES" : "❌ NO" ) . "\n<br>";

echo "<h2>Summary</h2>\n";
if ( $pro_active && $bookly_pro_exists && $proxy_pro_exists ) {
    echo "🎉 <strong>SUCCESS!</strong> Bookly PRO features have been successfully unlocked!\n<br>";
    echo "<ul>\n";
    echo "<li>✅ Unlimited services and staff members</li>\n";
    echo "<li>✅ Payment gateways enabled</li>\n";
    echo "<li>✅ Advanced features unlocked</li>\n";
    echo "<li>✅ Proxy system working</li>\n";
    echo "</ul>\n";
} else {
    echo "❌ <strong>ISSUES DETECTED!</strong> Some PRO features may not be working correctly.\n<br>";
}

echo "<p><em>Test completed at " . date( 'Y-m-d H:i:s' ) . "</em></p>\n";
?>
